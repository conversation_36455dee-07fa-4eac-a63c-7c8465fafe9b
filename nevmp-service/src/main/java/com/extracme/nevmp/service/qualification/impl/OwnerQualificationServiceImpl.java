package com.extracme.nevmp.service.qualification.impl;

import com.alibaba.fastjson.JSON;
import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.api.ResultCode;
import com.extracme.nevmp.config.SpringContextUtil;
import com.extracme.nevmp.constant.ApiConst;
import com.extracme.nevmp.constant.Const;
import com.extracme.nevmp.converter.ConvertUtil;
import com.extracme.nevmp.dto.SaveOperateLogDTO;
import com.extracme.nevmp.dto.common.FileInfoDTO;
import com.extracme.nevmp.dto.common.PageInfoBO;
import com.extracme.nevmp.dto.exchange.*;
import com.extracme.nevmp.dto.library.*;
import com.extracme.nevmp.dto.owner.*;
import com.extracme.nevmp.dto.qualification.owner.*;
import com.extracme.nevmp.enums.*;
import com.extracme.nevmp.error.ServiceException;
import com.extracme.nevmp.mapper.*;
import com.extracme.nevmp.mapper.extend.OwnerQualificationExtendMapper;
import com.extracme.nevmp.model.*;
import com.extracme.nevmp.service.FileService;
import com.extracme.nevmp.service.OwnerTrafficQualificationService;
import com.extracme.nevmp.service.UserOperateLogService;
import com.extracme.nevmp.service.async.AsyncService;
import com.extracme.nevmp.service.library.LibraryService;
import com.extracme.nevmp.service.qualification.*;
import com.extracme.nevmp.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Nullable;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.extracme.nevmp.mapper.OwnerIdentityInfoDynamicSqlSupport.ownerIdentityInfo;
import static com.extracme.nevmp.mapper.OwnerIdentityInfoDynamicSqlSupport.status;
import static com.extracme.nevmp.mapper.OwnerQualificationDynamicSqlSupport.ownerQualification;
import static com.extracme.nevmp.mapper.OwnerQualificationReviewDetailDynamicSqlSupport.ownerQualificationReviewDetail;
import static com.extracme.nevmp.mapper.OwnerResidencePermitPointQualificationDynamicSqlSupport.ownerResidencePermitPointQualification;
import static com.extracme.nevmp.mapper.OwnerTrafficQualificationDynamicSqlSupport.ownerTrafficQualification;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * 意向购车用户服务
 *
 * <AUTHOR>
 * @Description
 */
@Slf4j
@Service
public class OwnerQualificationServiceImpl implements OwnerQualificationService {

    Logger logger = LoggerFactory.getLogger(OwnerQualificationServiceImpl.class);

    @Autowired
    private OwnerQualificationExtendMapper ownerQualificationMapper;

    @Autowired
    private OwnerIdentityInfoMapper ownerIdentityInfoMapper;

    @Autowired
    private OwnerQualificationReviewDetailMapper ownerQualificationReviewDetailMapper;

    @Autowired
    private OnethingApplyInfoMapper onethingApplyInfoMapper;

    @Autowired
    private OwnerTrafficCommitteeQualificationMapper ownerTrafficCommitteeQualificationMapper;

    @Autowired
    private OwnerTrafficCommitteeExchangeMapper ownerTrafficCommitteeExchangeMapper;

    @Autowired
    private OwnerTrafficQualificationService ownerTrafficQualificationService;

    @Autowired
    private OwnerQualificationSpecialService ownerQualificationSpecialService;

    @Autowired
    private FileService fileService;

    @Autowired
    private SocialService socialService;

    @Autowired
    private NewSocialService newSocialService;

    @Autowired
    private CreditService creditService;

    @Autowired
    private DriverService driverService;

    @Autowired
    private ResidencePermitService residencePermitService;

    @Autowired
    private TrafficCommitteeService trafficCommitteeService;

    @Autowired
    private LibraryService libraryService;

    @Autowired
    private UserOperateLogService userOperateLogService;

    @Autowired
    private HouseholdRegistrationService householdRegistrationService;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    private OwnerResidencePermitPointQualificationMapper ownerResidencePermitPointQualificationMapper;

    // 短信内容常量
    public static final String MESSAGE_CONTENT_2024 = "经查询，您的新能源汽车专用牌照申领一件事“购车资格查询”事项已通过，" +
            "符合本轮《上海市鼓励购买和使用新能源汽车实施办法》专用牌照额度的申领条件，有效期至2024年12月31日。下一步为新能源汽车专用牌照申领一件事“车辆信息确认”事项，" +
            "请您与拟购车辆所属的新能源汽车生产厂 商做好沟通，待新能源汽车生产厂商上传相关信息后，通过“一网通办”签署上海市购买和使用新能源汽车安全承诺书。";

    public static final String MESSAGE_CONTENT = "经查询，您的新能源汽车专用牌照申领一件事“购车资格查询”事项已通过，" +
            "符合本轮《上海市鼓励购买和使用新能源汽车实施办法》专用牌照额度的申领条件，有效期为90天。下一步为新能源汽车专用牌照申领一件事“车辆信息确认”事项，" +
            "请您与拟购车辆所属的新能源汽车生产厂 商做好沟通，待新能源汽车生产厂商上传相关信息后，通过“一网通办”签署上海市购买和使用新能源汽车安全承诺书。";

    // 短信内容模板
    private static final String DENY_MESSAGE_TEMPLATE = "经查询，您的新能源购车资格申请未通过，暂不符合新能源汽车专用牌照申领条件，原因为：%s;如有其他问题，可于工作日上午9:00-11:30，下午1:30-5:00咨询021-58812035。";
    private static final String SUPPLEMENT_MESSAGE_TEMPLATE = "经查询，您的新能源购车资格申请未通过，暂不符合新能源汽车专用牌照申领条件，原因为：%s 您可选择：1、于30天内提交纳税记录等补正材料（需通过拟购品牌4S店上传，确保格式/内容正确）2、重新申请：拨打021-58812035或021-58810215，按接线员指引操作（工作日上午9:00-11:30，下午1:30-5:00）。";

    // ==================== 业务常量定义 ====================

    // 用户性质常量
    private static final int PROPERTY_PRIVATE = 1;    // 私人用户
    private static final int PROPERTY_BUSINESS = 2;   // 企业用户

    // 户籍类型常量
    private static final int HOUSEHOLD_LOCAL = 0;     // 上海户籍
    private static final int HOUSEHOLD_EXTERNAL = 1;  // 外省市户籍

    // 军官标识常量
    private static final int NOT_MILITARY_OFFICER = 0; // 非军官
    private static final int IS_MILITARY_OFFICER = 1;  // 军官

    // 审核状态常量
    private static final int REVIEW_STATUS_PENDING = 0;  // 待审核
    private static final int REVIEW_STATUS_APPROVE = 1;  // 审核通过
    private static final int REVIEW_STATUS_DENY = 2;     // 审核拒绝
    private static final int REVIEW_STATUS_NOT_FOUND = 3; // 未查询到信息

    // 审核类型常量
    private static final int REVIEW_TYPE_DRIVER = 0;           // 公安-驾照审核
    private static final int REVIEW_TYPE_CREDIT = 1;           // 信用审核
    private static final int REVIEW_TYPE_SOCIAL = 2;           // 社保审核
    private static final int REVIEW_TYPE_RESIDENCE = 3;        // 居住证审核
    private static final int REVIEW_TYPE_TRAFFIC_LICENSE = 4;  // 交通委-名下大牌额度审核
    private static final int REVIEW_TYPE_EXCHANGE = 5;         // 交通委-以旧换新额度审核

    // 车型类型常量
    private static final int VEHICLE_TYPE_HYBRID = 2;    // 插电式混合动力车

    // 时间常量
    private static final long QUALIFICATION_VALID_DAYS = 90L;  // 购车资格有效期（天）
    private static final long RECONSIDERATION_VALID_DAYS = 30L; // 复核有效期（天）

    // 认证类型常量
    private static final int AUTH_TYPE_ID_CARD = 1;      // 身份证

    // ==================== 内部类定义 ====================

    /**
     * 审核分析结果内部类
     * 用于封装审核结果的分析数据，包括是否完成审核、是否拒绝、拒绝原因等
     */
    private static class ReviewAnalysisResult {
        private boolean finishReview;  // 是否完成审核
        private boolean isDeny;        // 是否拒绝
        private String reason;         // 拒绝原因

        public ReviewAnalysisResult(boolean finishReview, boolean isDeny, String reason) {
            this.finishReview = finishReview;
            this.isDeny = isDeny;
            this.reason = reason;
        }

        public boolean isFinishReview() { return finishReview; }
        public void setFinishReview(boolean finishReview) { this.finishReview = finishReview; }

        public boolean isDeny() { return isDeny; }
        public void setDeny(boolean deny) { isDeny = deny; }

        public String getReason() { return reason; }
    }


    /**
     * 搜索购车资格信息
     * 支持多种条件的组合查询，包括用户信息、申请状态、时间范围等
     *
     * @param searchOwnerQualificationDTO 搜索条件DTO，包含分页信息和各种筛选条件
     * @return 分页查询结果，包含符合条件的购车资格列表和总数
     */
    @Override
    public PageInfoBO<OwnerQualification> searchOwnerQualification(SearchOwnerQualificationDTO searchOwnerQualificationDTO) {
        // 预处理模糊查询条件，为姓名添加通配符
        preprocessSearchConditions(searchOwnerQualificationDTO);

        // 构建分页查询SQL语句
        SelectStatementProvider selectOwner = buildOwnerQualificationSelectStatement(searchOwnerQualificationDTO, true);
        List<OwnerQualification> ownerQualificationList = ownerQualificationMapper.selectMany(selectOwner);

        // 构建计数查询SQL语句，获取符合条件的总记录数
        SelectStatementProvider countSql = buildOwnerQualificationSelectStatement(searchOwnerQualificationDTO, false);
        Long count = ownerQualificationMapper.count(countSql);

        return new PageInfoBO<>(count, ownerQualificationList);
    }

    /**
     * 预处理搜索条件
     * 主要用于处理模糊查询条件，为姓名字段添加SQL通配符
     *
     * @param searchDTO 搜索条件DTO，包含各种查询参数
     */
    private void preprocessSearchConditions(SearchOwnerQualificationDTO searchDTO) {
        // 为姓名模糊查询添加通配符，支持部分匹配
        if (StringUtils.isNotBlank(searchDTO.getNameLike())) {
            searchDTO.setNameLike("%" + searchDTO.getNameLike() + "%");
        }
    }

    /**
     * 构建购车资格查询SQL语句
     * 根据搜索条件动态构建查询语句，支持分页查询和计数查询两种模式
     *
     * @param searchDTO 搜索条件DTO，包含各种筛选参数
     * @param withPagination 是否包含分页信息，true为分页查询，false为计数查询
     * @return SQL查询语句提供者，用于MyBatis执行
     */
    private SelectStatementProvider buildOwnerQualificationSelectStatement(SearchOwnerQualificationDTO searchDTO, boolean withPagination) {
        if (withPagination) {
            // 构建分页查询语句，包含排序和分页限制
            return SqlBuilder.select(ownerQualification.allColumns())
                    .from(ownerQualification)
                    .where()
                    // 基础条件：用户性质、认证类型、认证ID
                    .and(ownerQualification.property, isEqualToWhenPresent(searchDTO.getProperty()))
                    .and(ownerQualification.authType, isEqualToWhenPresent(searchDTO.getAuthType()))
                    .and(ownerQualification.authId, isInWhenPresent(searchDTO.getAuthId()))
                    // 申请状态和用户信息
                    .and(ownerQualification.applyStatus, isEqualToWhenPresent(searchDTO.getApplyStatus()))
                    .and(ownerQualification.name, isEqualToWhenPresent(searchDTO.getName()))
                    .and(ownerQualification.name, isLikeWhenPresent(searchDTO.getNameLike()))
                    .and(ownerQualification.householdRegistrationType, isEqualToWhenPresent(searchDTO.getHouseholdRegistrationType()))
                    // 申请时间范围查询
                    .and(ownerQualification.applyTime, isGreaterThanOrEqualToWhenPresent(searchDTO.getApplyTimeStart()))
                    .and(ownerQualification.applyTime, isLessThanWhenPresent(searchDTO.getApplyTimeEnd()))
                    // 审核时间范围查询
                    .and(ownerQualification.reviewTime, isGreaterThanOrEqualToWhenPresent(searchDTO.getReviewTimeStart()))
                    .and(ownerQualification.reviewTime, isLessThanWhenPresent(searchDTO.getReviewTimeEnd()))
                    // 以旧换新相关条件
                    .and(ownerQualification.exchangeStatus, isEqualToWhenPresent(searchDTO.getExchangeStatus()))
                    .and(ownerQualification.exchangeApplyTime, isGreaterThanOrEqualToWhenPresent(searchDTO.getExchangeApplyTimeStart()))
                    .and(ownerQualification.exchangeApplyTime, isLessThanWhenPresent(searchDTO.getExchangeApplyTimeEnd()))
                    .and(ownerQualification.exchangeReviewTime, isGreaterThanOrEqualToWhenPresent(searchDTO.getExchangeReviewTimeStart()))
                    .and(ownerQualification.exchangeReviewTime, isLessThanWhenPresent(searchDTO.getExchangeReviewTimeEnd()))
                    // 固定条件：状态为有效，指定资格类型
                    .and(ownerQualification.status, isEqualTo(1))
                    .and(ownerQualification.qualificationType, isEqualTo(searchDTO.getQualificationType()))
                    // 排序和分页
                    .orderBy(ownerQualification.id)
                    .limit(searchDTO.getPageSize())
                    .offset((searchDTO.getPageNum() - 1) * searchDTO.getPageSize())
                    .build()
                    .render(RenderingStrategies.MYBATIS3);
        } else {
            // 构建计数查询语句，用于获取总记录数
            return countFrom(ownerQualification)
                    .where()
                    // 与分页查询使用相同的筛选条件
                    .and(ownerQualification.property, isEqualToWhenPresent(searchDTO.getProperty()))
                    .and(ownerQualification.authType, isEqualToWhenPresent(searchDTO.getAuthType()))
                    .and(ownerQualification.authId, isInWhenPresent(searchDTO.getAuthId()))
                    .and(ownerQualification.applyStatus, isEqualToWhenPresent(searchDTO.getApplyStatus()))
                    .and(ownerQualification.name, isEqualToWhenPresent(searchDTO.getName()))
                    .and(ownerQualification.name, isLikeWhenPresent(searchDTO.getNameLike()))
                    .and(ownerQualification.householdRegistrationType, isEqualToWhenPresent(searchDTO.getHouseholdRegistrationType()))
                    .and(ownerQualification.applyTime, isGreaterThanOrEqualToWhenPresent(searchDTO.getApplyTimeStart()))
                    .and(ownerQualification.applyTime, isLessThanWhenPresent(searchDTO.getApplyTimeEnd()))
                    .and(ownerQualification.reviewTime, isGreaterThanOrEqualToWhenPresent(searchDTO.getReviewTimeStart()))
                    .and(ownerQualification.reviewTime, isLessThanWhenPresent(searchDTO.getReviewTimeEnd()))
                    .and(ownerQualification.exchangeStatus, isEqualToWhenPresent(searchDTO.getExchangeStatus()))
                    .and(ownerQualification.exchangeApplyTime, isGreaterThanOrEqualToWhenPresent(searchDTO.getExchangeApplyTimeStart()))
                    .and(ownerQualification.exchangeApplyTime, isLessThanWhenPresent(searchDTO.getExchangeApplyTimeEnd()))
                    .and(ownerQualification.exchangeReviewTime, isGreaterThanOrEqualToWhenPresent(searchDTO.getExchangeReviewTimeStart()))
                    .and(ownerQualification.exchangeReviewTime, isLessThanWhenPresent(searchDTO.getExchangeReviewTimeEnd()))
                    .and(ownerQualification.status, isEqualTo(1))
                    .and(ownerQualification.qualificationType, isEqualTo(searchDTO.getQualificationType()))
                    .build()
                    .render(RenderingStrategies.MYBATIS3);
        }
    }

    /**
     * 根据ID获取购车资格详情信息
     * 这是对外提供的主要接口，通过ID查询并返回完整的购车资格详情
     *
     * @param id 购车资格ID
     * @return 购车资格详情DTO，包含用户信息、审核状态、文件信息等
     * @throws ServiceException 当未找到对应的购车资格信息时抛出
     */
    @Override
    public OwnerQualificationDetailDTO getOwnerQualificationDetail(Long id) {
        // 根据ID查询购车资格基础信息
        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectByPrimaryKey(id);
        if (!optionalOwnerQualification.isPresent()) {
            throw new ServiceException("未查询到相应用户信息");
        }
        // 调用内部方法获取详细信息
        return getOwnerQualificationDetail(optionalOwnerQualification);
    }

    /**
     * 根据ID获取购车资格基础信息
     * 简单的查询方法，仅返回基础的购车资格实体对象，不包含关联信息
     *
     * @param id 购车资格ID
     * @return 购车资格实体对象，如果ID为空或未找到则返回null
     */
    @Override
    public OwnerQualification getOwnerQualificationById(Long id) {
        // 参数校验：ID为空时直接返回null
        if(id == null){
            return null;
        }
        // 查询并返回购车资格基础信息，未找到时返回null
        return ownerQualificationMapper.selectByPrimaryKey(id).orElse(null);
    }


    /**
     * 获取购车资格详情信息
     * 重构说明：将原来的复杂方法拆分为多个职责单一的私有方法，提高代码可读性和可维护性
     * @param optionalOwnerQualification 购车资格信息
     * @return 购车资格详情DTO
     */
    private OwnerQualificationDetailDTO getOwnerQualificationDetail(Optional<OwnerQualification> optionalOwnerQualification) {
        OwnerQualification qualification = optionalOwnerQualification.get();
        OwnerQualificationDetailDTO detailDTO = ConvertUtil.normalConvert(qualification, OwnerQualificationDetailDTO.class);
        detailDTO.setReviewStatus(qualification.getApplyStatus());

        // 填充驾照信息
        fillDriverLicenseInfo(detailDTO, qualification.getId());

        // 填充文件信息
        fillFileInfo(detailDTO, qualification);

        // 填充居住证积分信息
        fillResidencePermitPointInfo(detailDTO);

        // 查询并处理审核详情
        List<OwnerQualificationReviewDetail> reviewDetailList = queryReviewDetailList(qualification.getId());
        processReviewDetails(detailDTO, reviewDetailList);

        // 设置复核状态
        setReconsiderationStatus(detailDTO, qualification);

        // 判断能否信用复核
        detailDTO.setCanCreditReconsider(validCreditCanReconsider(qualification, reviewDetailList));

        return detailDTO;
    }

    /**
     * 填充驾照信息
     * @param detailDTO 详情DTO
     * @param qualificationId 资格ID
     */
    private void fillDriverLicenseInfo(OwnerQualificationDetailDTO detailDTO, Long qualificationId) {
        OwnerTrafficQualification trafficQualification = ownerTrafficQualificationService.queryOwnerTrafficQualification(qualificationId);
        if (trafficQualification != null) {
            detailDTO.setDriverLicenseCode(trafficQualification.getDriverLicenseCode());
            detailDTO.setDriverLicenseIssuingPlace(trafficQualification.getDriverLicenseIssuingPlace());
            detailDTO.setDriverLicenseIssuingOrganization(trafficQualification.getDriverLicenseIssuingOrganization());
            detailDTO.setDriverFileNo(trafficQualification.getDriverFileNo());
        }
    }

    /**
     * 填充文件信息（资格证明材料、军官证明材料、以旧换新证明材料）
     * @param detailDTO 详情DTO
     * @param qualification 购车资格信息
     */
    private void fillFileInfo(OwnerQualificationDetailDTO detailDTO, OwnerQualification qualification) {
        Long qualificationId = qualification.getId();

        // 用户资格证明材料
        List<FileInfoDTO> reconsiderationFile = fileService.getFileInfo(FileTypeEnum.QUALIFICATION_PROOF_COPY, qualificationId);
        detailDTO.setReconsiderationFileCopy(reconsiderationFile);

        // 军官证明材料
        List<FileInfoDTO> militaryLicenseFile = fileService.getFileInfo(FileTypeEnum.MILITARY_OFFICER_COPY, qualificationId);
        detailDTO.setMilitaryLicenseFile(militaryLicenseFile);

        // 以旧换新证明材料
        if (Objects.equals(OwnerQualificationTypeEnum.EXCHANGE.getType(), qualification.getQualificationType())) {
            List<FileInfoDTO> exchangeProofFile = fileService.getFileInfo(FileTypeEnum.EXCHANGE_PROOF_COPY, qualificationId);
            detailDTO.setExchangeProofFile(exchangeProofFile);
        }
    }

    /**
     * 填充居住证积分信息
     * @param detailDTO 详情DTO
     */
    private void fillResidencePermitPointInfo(OwnerQualificationDetailDTO detailDTO) {
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerResidencePermitPointQualification.allColumns())
                .from(ownerResidencePermitPointQualification)
                .where()
                .and(ownerResidencePermitPointQualification.ownerQualificationId, isEqualTo(detailDTO.getId()))
                .and(ownerResidencePermitPointQualification.status, isEqualTo(1))
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);

        Optional<OwnerResidencePermitPointQualification> permitPointQualification =
                ownerResidencePermitPointQualificationMapper.selectOne(selectStatement);

        if (permitPointQualification.isPresent()) {
            Integer hasEnoughPoint = permitPointQualification.get().getHasEnoughPoint();
            if (Integer.valueOf(1).equals(hasEnoughPoint)) {
                detailDTO.setHasEnoughPoint(1);
            } else if (Integer.valueOf(0).equals(hasEnoughPoint)) {
                detailDTO.setHasEnoughPoint(0);
            }
        }
    }

    /**
     * 查询审核详情列表
     * @param qualificationId 资格ID
     * @return 审核详情列表
     */
    private List<OwnerQualificationReviewDetail> queryReviewDetailList(Long qualificationId) {
        SelectStatementProvider selectSql = select(ownerQualificationReviewDetail.allColumns())
                .from(ownerQualificationReviewDetail)
                .where(ownerQualificationReviewDetail.ownerQulificationId, isEqualTo(qualificationId))
                .build().render(RenderingStrategies.MYBATIS3);
        return ownerQualificationReviewDetailMapper.selectMany(selectSql);
    }

    /**
     * 处理审核详情信息，根据不同的审核类型设置相应的审核结果
     * @param detailDTO 详情DTO
     * @param reviewDetailList 审核详情列表
     */
    private void processReviewDetails(OwnerQualificationDetailDTO detailDTO, List<OwnerQualificationReviewDetail> reviewDetailList) {
        reviewDetailList.forEach(reviewDetail ->
            Optional.ofNullable(reviewDetail.getReviewType()).ifPresent(type -> {
                BaseOwnerQualificationReviewDetail baseReviewDetail = ConvertUtil.normalConvert(reviewDetail, BaseOwnerQualificationReviewDetail.class);

                switch (type) {
                    case REVIEW_TYPE_DRIVER: // 公安-驾照
                        detailDTO.setOwnerTrafficReviewDetail(baseReviewDetail);
                        break;
                    case REVIEW_TYPE_CREDIT: // 信用
                        detailDTO.setOwnerCreditReviewDetail(baseReviewDetail);
                        processCreditReviewDetail(detailDTO);
                        break;
                    case REVIEW_TYPE_SOCIAL: // 社保
                        detailDTO.setOwnerSocialInsuranceReviewDetail(baseReviewDetail);
                        processSocialInsuranceReviewDetail(detailDTO);
                        break;
                    case REVIEW_TYPE_RESIDENCE: // 居住证
                        detailDTO.setResidencePermitReviewDetail(baseReviewDetail);
                        break;
                    case REVIEW_TYPE_TRAFFIC_LICENSE: // 交通委-名下大牌额度
                        detailDTO.setNonOperationalReviewDetail(baseReviewDetail);
                        break;
                    case REVIEW_TYPE_EXCHANGE: // 交通委-以旧换新额度
                        detailDTO.setOwnerExchangeReviewDetail(baseReviewDetail);
                        break;
                    default:
                        break;
                }
            })
        );
    }

    /**
     * 处理信用审核详情的复核描述信息
     * @param detailDTO 详情DTO
     */
    private void processCreditReviewDetail(OwnerQualificationDetailDTO detailDTO) {
        // 针对信用审核未查询到相关信息且为私人用户的情况，添加复核描述
        if (detailDTO.getOwnerCreditReviewDetail().getReviewStatus() == 3
            && detailDTO.getAuthType() != 1
            && detailDTO.getProperty() == 1) {
            appendReconsiderDescribe(detailDTO, "信用相关材料");
        }
    }

    /**
     * 处理社保审核详情的复核描述信息
     * @param detailDTO 详情DTO
     */
    private void processSocialInsuranceReviewDetail(OwnerQualificationDetailDTO detailDTO) {
        if (detailDTO.getProperty() == 1) { // 私人用户
            processSocialInsuranceForPrivateUser(detailDTO);
        } else if (detailDTO.getProperty() == 2) { // 企业用户
            processSocialInsuranceForBusinessUser(detailDTO);
        }
    }

    /**
     * 处理私人用户的社保审核详情
     * @param detailDTO 详情DTO
     */
    private void processSocialInsuranceForPrivateUser(OwnerQualificationDetailDTO detailDTO) {
        int reviewStatus = detailDTO.getOwnerSocialInsuranceReviewDetail().getReviewStatus();
        if (reviewStatus == 2) { // 审核拒绝
            boolean hasEnoughPoint = Integer.valueOf(1).equals(detailDTO.getHasEnoughPoint());
            boolean isForeigner = detailDTO.getAuthType() != 1;

            if (hasEnoughPoint || isForeigner) {
                // 居住证积分满足或外籍人士
                appendReconsiderDescribe(detailDTO, "前6个月个税税单");
            } else {
                // 其他情况
                appendReconsiderDescribe(detailDTO, "前36个月个税税单");
            }
        }
    }

    /**
     * 处理企业用户的社保审核详情
     * @param detailDTO 详情DTO
     */
    private void processSocialInsuranceForBusinessUser(OwnerQualificationDetailDTO detailDTO) {
        if (detailDTO.getOwnerSocialInsuranceReviewDetail().getReviewStatus() == 2) {
            appendReconsiderDescribe(detailDTO, "前1年完税证明");
        }
    }

    /**
     * 追加复核描述信息
     * @param detailDTO 详情DTO
     * @param description 描述信息
     */
    private void appendReconsiderDescribe(OwnerQualificationDetailDTO detailDTO, String description) {
        if (detailDTO.getReconsiderDescribe() == null) {
            detailDTO.setReconsiderDescribe(description);
        } else {
            detailDTO.setReconsiderDescribe(detailDTO.getReconsiderDescribe() + "|" + description + "|");
        }
    }

    /**
     * 设置复核状态
     * @param detailDTO 详情DTO
     * @param qualification 购车资格信息
     */
    private void setReconsiderationStatus(OwnerQualificationDetailDTO detailDTO, OwnerQualification qualification) {
        // 确保当前在复核有效期之前且状态为拒绝
        boolean canReconsider = qualification.getExpireTime() != null
            && new Date().before(qualification.getExpireTime())
            && OwnerQualificationReviewStatusEnum.DENY.getStatus().equals(detailDTO.getReviewStatus());

        detailDTO.setReconsiderationStatus(canReconsider ? 1 : 0);
    }

    // ==================== 通用工具方法 ====================

    /**
     * 发送审核通过短信
     * 根据当前时间自动选择合适的短信模板内容
     *
     * @param mobilePhone 接收短信的手机号码
     */
    private void sendApprovalMessage(String mobilePhone) {
        String content = getApprovalMessageContent();
        MessageUtil.sendMessage(mobilePhone, content);
    }

    /**
     * 发送审核拒绝短信
     * 使用标准的拒绝短信模板，包含拒绝原因和咨询电话
     *
     * @param mobilePhone 接收短信的手机号码
     * @param reason 审核拒绝的具体原因
     */
    private void sendDenyMessage(String mobilePhone, String reason) {
        String content = String.format(DENY_MESSAGE_TEMPLATE, reason);
        MessageUtil.sendMessage(mobilePhone, content);
    }

    /**
     * 发送补正材料短信
     * 通知用户需要提交补正材料，包含操作指引和咨询电话
     *
     * @param mobilePhone 接收短信的手机号码
     * @param reason 需要补正的具体原因
     */
    private void sendSupplementMessage(String mobilePhone, String reason) {
        String content = String.format(SUPPLEMENT_MESSAGE_TEMPLATE, reason);
        MessageUtil.sendMessage(mobilePhone, content);
    }

    /**
     * 获取审核通过短信内容
     * 根据当前时间判断使用2024年版本还是常规版本的短信模板
     *
     * @return 短信内容字符串
     */
    private String getApprovalMessageContent() {
        // 2025年1月1日前使用2024年特殊版本，之后使用常规版本
        if (new Date().before(DateUtil.parse("2025-01-01 00:00:00", DateUtil.DATE_TYPE1))) {
            return MESSAGE_CONTENT_2024;
        } else {
            return MESSAGE_CONTENT;
        }
    }

    /**
     * 更新购车资格状态的通用方法
     * 统一处理购车资格状态变更，包括审核状态、原因、有效期等信息的更新
     *
     * @param qualificationId 购车资格ID
     * @param status 新的申请状态（审核中、通过、拒绝等）
     * @param reason 状态变更原因，通常用于记录拒绝原因
     * @param expireTime 有效期时间，通过时设置90天有效期，拒绝时可设置复核期限
     * @param operatorId 操作人员ID
     * @param operatorName 操作人员姓名
     */
    private void updateQualificationStatus(Long qualificationId, Integer status, String reason,
                                         Date expireTime, String operatorId, String operatorName) {
        Date now = new Date();
        UpdateStatementProvider updateStatement = SqlBuilder.update(ownerQualification)
                .set(ownerQualification.applyStatus).equalTo(status)
                .set(ownerQualification.reason).equalTo(reason)
                .set(ownerQualification.expireTime).equalToWhenPresent(expireTime)
                .set(ownerQualification.reviewTime).equalTo(now)
                .set(ownerQualification.updatedTime).equalTo(now)
                .set(ownerQualification.updatedUserId).equalTo(operatorId)
                .set(ownerQualification.updatedUserName).equalTo(operatorName)
                .where(ownerQualification.id, isEqualTo(qualificationId))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        ownerQualificationMapper.update(updateStatement);
    }

    /**
     * 计算购车资格有效期（90天后）
     * 购车资格审核通过后的标准有效期为90天，用户需在此期间内完成车辆绑定
     *
     * @return 90天后的有效期时间
     */
    private Date calculateExpirationTime() {
        return new Date(System.currentTimeMillis() + TimeUnit.DAYS.toMillis(QUALIFICATION_VALID_DAYS));
    }

    /**
     * 计算复核有效期（30天后）
     * 购车资格被拒绝后，用户有30天时间提交复核材料
     *
     * @return 30天后的复核截止时间
     */
    private Date calculateReconsiderationExpirationTime() {
        return new Date(System.currentTimeMillis() + TimeUnit.DAYS.toMillis(RECONSIDERATION_VALID_DAYS));
    }


    // ==================== 业务逻辑方法 ====================

    /**
     * 判断信用审核是否可以复核
     * 主要用于处理刑事案件执行期内数据丢失的特殊情况
     *
     * @param ownerQualification 购车资格信息
     * @return true表示信用可复核，false表示信用不可复核
     */
    private Boolean validCreditCanReconsider(OwnerQualification ownerQualification){
        // 查询该购车资格的所有审核详情
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerQualificationReviewDetail.allColumns())
                .from(ownerQualificationReviewDetail)
                .where(ownerQualificationReviewDetail.ownerQulificationId, isEqualTo(ownerQualification.getId()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        List<OwnerQualificationReviewDetail> ownerQualificationReviewDetailList = ownerQualificationReviewDetailMapper.selectMany(selectStatement);

        // 调用重载方法进行具体的复核条件判断
        return validCreditCanReconsider(ownerQualification, ownerQualificationReviewDetailList);
    }

    /**
     * 判断信用审核是否可以复核（重载方法）
     * 按照正常流程，信用不通过不能复核
     * 但是由于刑事案件的执行期内数据丢失，针对未在刑事期内的用户需要有渠道能够手动复核
     *
     * @param ownerQualification 购车资格信息
     * @param reviewDetailList 审核详情列表
     * @return true表示信用可复核，false表示信用不可复核
     */
    private Boolean validCreditCanReconsider(OwnerQualification ownerQualification,List<OwnerQualificationReviewDetail> reviewDetailList){
        // 校验时间限制：审核拒绝日期不能超过当前时间30天
        if (!isWithinReconsiderationTimeLimit(ownerQualification)) {
            return false;
        }

        // 只有信用审核导致最终结果不通过的用户，才允许进行复核
        if (!OwnerQualificationStatusEnum.DENY.getStatus().equals(ownerQualification.getApplyStatus())) {
            return false;
        }

        // 检查各项审核详情，判断是否符合信用复核条件
        return checkCreditReconsiderationEligibility(reviewDetailList);
    }

    /**
     * 检查是否在复核时间限制内
     * @param ownerQualification 购车资格信息
     * @return true表示在时间限制内，false表示超出时间限制
     */
    private boolean isWithinReconsiderationTimeLimit(OwnerQualification ownerQualification) {
        if (ownerQualification.getReviewTime() == null) {
            return false;
        }
        long timeDifference = new Date().getTime() - ownerQualification.getReviewTime().getTime();
        return timeDifference <= TimeUnit.DAYS.toMillis(RECONSIDERATION_VALID_DAYS);
    }

    /**
     * 检查信用复核资格
     * 只有信用审核未通过且其他审核项目都通过的情况下，才允许信用复核
     *
     * @param reviewDetailList 审核详情列表
     * @return true表示符合复核条件，false表示不符合
     */
    private boolean checkCreditReconsiderationEligibility(List<OwnerQualificationReviewDetail> reviewDetailList) {
        for(OwnerQualificationReviewDetail reviewDetail : reviewDetailList){
            if(reviewDetail.getReviewType() == REVIEW_TYPE_CREDIT){ // 信用审核信息
                // 已审核通过的信用不能复核
                if(OwnerQualificationReviewStatusEnum.APPROVE.getStatus().equals(reviewDetail.getReviewStatus())){
                    return false;
                }
            } else { // 其他审核项目
                // 其他项目被拒绝的不能进行信用复核
                if(OwnerQualificationReviewStatusEnum.DENY.getStatus().equals(reviewDetail.getReviewStatus())){
                    return false;
                }
            }
        }
        return true;
    }

    @Override
    public OwnerQualificationDetailDTO queryOwnerQualificationDetail(Integer authType, String authId) {
        //查询最近一条意向用户记录
        SelectStatementProvider selectStatement = select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where()
                .and(ownerQualification.authId, isEqualTo(authId))
                .and(ownerQualification.authType, isEqualTo(authType))
                .and(ownerQualification.qualificationType, isEqualTo(OwnerQualificationTypeEnum.NORMAL.getType()))
                .and(ownerQualification.status, isEqualTo(1))
                .orderBy(ownerQualification.id.descending())
                .limit(1)
                .build().render(RenderingStrategies.MYBATIS3);
        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectOne(selectStatement);
        if (!optionalOwnerQualification.isPresent()) {
            throw new ServiceException("未查到相关用户信息:" + authId);
        }
        // 针对旧表的意向用户信息查询
        return getOwnerQualificationDetail(optionalOwnerQualification);
    }

    @Override
    public OwnerQualificationDetailDTO queryOwnerExchangeQualification(Integer authType, String authId, String exchangeVehicleNo) {
        //根据用户证件号及以旧换新车架号查询相关记录
        SelectStatementProvider selectStatement = select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where()
                .and(ownerQualification.authId, isEqualTo(authId))
                .and(ownerQualification.authType, isEqualTo(authType))
                .and(ownerQualification.exchangeVehicleNo, isEqualTo(exchangeVehicleNo))
                .and(ownerQualification.qualificationType, isEqualTo(OwnerQualificationTypeEnum.EXCHANGE.getType()))
                .and(ownerQualification.status, isEqualTo(1))
                .orderBy(ownerQualification.id.descending())
                .limit(1)
                .build().render(RenderingStrategies.MYBATIS3);
        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectOne(selectStatement);
        if (!optionalOwnerQualification.isPresent()) {
            throw new ServiceException("未查到相关用户信息:" + authId);
        }
        // 针对旧表的意向用户信息查询
        return getOwnerQualificationDetail(optionalOwnerQualification);
    }

    @Override
    public String queryOwnerMobilePhone(int authType, String authId) {
        String result = StringUtils.EMPTY;
        //查询最近一条意向用户记录
        SelectStatementProvider selectStatement = select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where()
                .and(ownerQualification.authId, isEqualTo(authId))
                .and(ownerQualification.authType, isEqualTo(authType))
                .and(ownerQualification.qualificationType, isEqualTo(OwnerQualificationTypeEnum.NORMAL.getType()))
                .and(ownerQualification.status,isEqualTo(1))
                .orderBy(ownerQualification.id.descending())
                .limit(1)
                .build().render(RenderingStrategies.MYBATIS3);
        OwnerQualification owner = ownerQualificationMapper.selectOne(selectStatement).orElse(null);
        if(owner != null){
            result = owner.getMobilePhone();
        }
        return result;
    }

    @Override
    public OwnerQualificationDetailHistoryDTO queryOwnerQualificationDetailHistory(String authType, String authId) {
        OwnerQualificationDetailHistoryDTO ownerQualificationDetailHistory = new OwnerQualificationDetailHistoryDTO();

        SelectStatementProvider selectStatement = SqlBuilder.select(ownerIdentityInfo.allColumns())
                .from(ownerIdentityInfo)
                .where(ownerIdentityInfo.authIdentityId, isEqualTo(authId))
                .and(ownerIdentityInfo.authIdentityKind, isEqualTo(authType))
                .and(ownerIdentityInfo.ownerIdApp, isNotEqualTo(0))
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        final Optional<OwnerIdentityInfo> optionalOwnerIdentityInfo = ownerIdentityInfoMapper.selectOne(selectStatement);
        if (optionalOwnerIdentityInfo.isPresent()) {
            ownerQualificationDetailHistory.setAuthId(optionalOwnerIdentityInfo.get().getAuthIdentityId());
            ownerQualificationDetailHistory.setAuthKind(optionalOwnerIdentityInfo.get().getAuthIdentityKind());
            ownerQualificationDetailHistory.setExpireTime(optionalOwnerIdentityInfo.get().getExpireTime());
            ownerQualificationDetailHistory.setName(optionalOwnerIdentityInfo.get().getOwnerIdentityName());
            ownerQualificationDetailHistory.setStatus(optionalOwnerIdentityInfo.get().getStatus());
            ownerQualificationDetailHistory.setRemark(optionalOwnerIdentityInfo.get().getRemark());
        }
        return ownerQualificationDetailHistory;
    }

    @Override
    public BaseResponse reconsiderFile(ReconsiderFileDTO reconsiderFileDTO) {
        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectByPrimaryKey(reconsiderFileDTO.getId());
        if (!optionalOwnerQualification.isPresent()) {
            throw new ServiceException("未查询到相应用户信息");
        }
        if (!OwnerQualificationStatusEnum.DENY.getStatus().equals(optionalOwnerQualification.get().getApplyStatus())) {
            throw new ServiceException("当前状态不能进行该操作");
        }

        // 可以简化成针对状态+有效期的判断
        //复核条件判断
        if(optionalOwnerQualification.get().getExpireTime() == null || optionalOwnerQualification.get().getExpireTime().compareTo(new Date()) <= 0){
            throw new ServiceException("当前状态不能进行该操作");
        }

        // 判断是否有照片，是否之前复核过，只能复核一次
        List<FileInfoDTO> fileInfo = fileService.getFileInfo(FileTypeEnum.QUALIFICATION_PROOF_COPY, optionalOwnerQualification.get().getId());
        List<FileInfoDTO> militaryFile = fileService.getFileInfo(FileTypeEnum.MILITARY_OFFICER_COPY,optionalOwnerQualification.get().getId());


        if (!fileInfo.isEmpty() || !militaryFile.isEmpty()) {
            throw new ServiceException("该记录已复核过，不可多次复核，请重新创建并提交申请");
        }

        //清空之前上传的图片
        fileService.clearFile(FileTypeEnum.QUALIFICATION_PROOF_COPY, optionalOwnerQualification.get().getId(), reconsiderFileDTO.getOperatorId(), reconsiderFileDTO.getOperatorName());
        //保存上传的文件
        List<String> collect = reconsiderFileDTO.getReconsiderationFileCopy().stream().map(FileInfoDTO::getRelativeFilePath).collect(Collectors.toList());
        fileService.saveFileInfo(FileTypeEnum.QUALIFICATION_PROOF_COPY, collect, reconsiderFileDTO.getId(),
                reconsiderFileDTO.getOperatorId(), reconsiderFileDTO.getOperatorName());


        //将办件状态更新为复核
        reconsiderQualification(optionalOwnerQualification.get().getId(), reconsiderFileDTO.getOperatorId()
                , reconsiderFileDTO.getOperatorName(), reconsiderFileDTO.getReconsiderOrgName());
        if (StringUtils.isNotBlank(optionalOwnerQualification.get().getApplyNo())) {
            //受理办件
            libraryService.acceptApply(optionalOwnerQualification.get().getApplyNo());
            //更新指南
            libraryService.changeStatus(optionalOwnerQualification.get().getApplyNo(), AnnouncementEnum.QUALIFICATION_ACCEPTED);
        }
        return new BaseResponse();
    }

    @Override
    @Deprecated
    public BaseResponse reconsiderCreditQualification(ReconsiderFileDTO reconsiderFileDTO) {
        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectByPrimaryKey(reconsiderFileDTO.getId());
        if (!optionalOwnerQualification.isPresent()) {
            throw new ServiceException("未查询到相应用户信息");
        }
        //复核条件判断
        boolean  canCreditReconsider = validCreditCanReconsider(optionalOwnerQualification.get());
        if (!canCreditReconsider) {
            throw new ServiceException("当前状态不能进行信用复核");
        }

        //清空之前上传的图片
        fileService.clearFile(FileTypeEnum.QUALIFICATION_PROOF_COPY, optionalOwnerQualification.get().getId(), reconsiderFileDTO.getOperatorId(), reconsiderFileDTO.getOperatorName());
        //保存上传的文件
        List<String> collect = reconsiderFileDTO.getReconsiderationFileCopy().stream().map(FileInfoDTO::getRelativeFilePath).collect(Collectors.toList());
        fileService.saveFileInfo(FileTypeEnum.QUALIFICATION_PROOF_COPY, collect, reconsiderFileDTO.getId(),
                reconsiderFileDTO.getOperatorId(), reconsiderFileDTO.getOperatorName());

        //修复信用资质
        recoverCreditQualification(optionalOwnerQualification.get().getId(), reconsiderFileDTO.getOperatorId(), reconsiderFileDTO.getOperatorName());

        //发送短信通知
        String content;
        if(new Date().before(DateUtil.parse("2025-01-01 00:00:00", DateUtil.DATE_TYPE1))){
            content = MESSAGE_CONTENT_2024;
        } else {
            content = MESSAGE_CONTENT;
        }
        MessageUtil.sendMessage(optionalOwnerQualification.get().getMobilePhone(), content);
        return new BaseResponse();
    }

    private void recoverCreditQualification(Long ownerQualificationId, String operatorId, String operatorName) {

        Date now = new Date();
        //复核意向用户
        UpdateStatementProvider updateStatement = SqlBuilder.update(ownerQualification)
                .set(ownerQualification.applyStatus).equalTo(OwnerQualificationStatusEnum.APPROVE.getStatus())
                .set(ownerQualification.reviewTime).equalTo(now)
                .set(ownerQualification.expireTime).equalTo(new Date(TimeUnit.DAYS.toMillis(90) + now.getTime()))
                .set(ownerQualification.updatedTime).equalTo(new Date())
                .set(ownerQualification.updatedUserId).equalTo(operatorId)
                .set(ownerQualification.updatedUserName).equalTo(operatorName)
                .where(ownerQualification.id, isEqualTo(ownerQualificationId))
                .and(ownerQualification.applyStatus, isEqualTo(OwnerQualificationStatusEnum.DENY.getStatus()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        ownerQualificationMapper.update(updateStatement);

        //保存操作日志
        SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                .userOperateTypeEnum(UserOperateTypeEnum.RECONSIDER_OWNER_CREDIT_QUALIFICATION)
                .ownerQualificationId(ownerQualificationId)
                .operateUserId(operatorId)
                .operateUserName(operatorName)
                .build();
        userOperateLogService.saveOperateLog(saveOperateLogDTO);
    }

    @Override
    public BaseResponse approveOwnerQualification(ApproveOwnerQualificationDTO approveOwnerQualificationDTO) {
        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectByPrimaryKey(approveOwnerQualificationDTO.getId());
        if (!optionalOwnerQualification.isPresent()) {
            throw new ServiceException("未查询到相应的用户信息");
        }
        if (!OwnerQualificationStatusEnum.RECONSIDERATION.getStatus().equals(optionalOwnerQualification.get().getApplyStatus())) {
            throw new ServiceException("当前状态不能进行该操作");
        }

        //更新意向用户状态
        Date now = new Date();
        OwnerQualification approveOwnerQualification = new OwnerQualification();
        approveOwnerQualification.setId(optionalOwnerQualification.get().getId());
        approveOwnerQualification.setApplyStatus(OwnerQualificationStatusEnum.APPROVE.getStatus());
        approveOwnerQualification.setReviewTime(now);
        approveOwnerQualification.setReason("");
        //有效期，需90天内绑定
        approveOwnerQualification.setExpireTime(new Date(TimeUnit.DAYS.toMillis(90) + now.getTime()));
        approveOwnerQualification.setUpdatedTime(new Date());
        approveOwnerQualification.setUpdatedUserId(approveOwnerQualificationDTO.getOperatorId());
        approveOwnerQualification.setUpdatedUserName(approveOwnerQualificationDTO.getOperatorName());
        ownerQualificationMapper.updateByPrimaryKeySelective(approveOwnerQualification);

        // 私人需判断购买的是插电式混动车还是非插电式混动车
        String content = StringUtils.EMPTY;
        if(new Date().before(DateUtil.parse("2025-01-01 00:00:00", DateUtil.DATE_TYPE1))){
            content = MESSAGE_CONTENT_2024;
        } else {
            content = MESSAGE_CONTENT;
        }
        MessageUtil.sendMessage(approveOwnerQualification.getMobilePhone(), content);


        //保存操作日志
        SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                .userOperateTypeEnum(UserOperateTypeEnum.APPROVE_OWNER_QUALIFICATION)
                .operateUserName(approveOwnerQualificationDTO.getOperatorName())
                .vehicleId(approveOwnerQualificationDTO.getId())
                .operateUserId(approveOwnerQualificationDTO.getOperatorId())

                .build();
        userOperateLogService.saveOperateLog(saveOperateLogDTO);

        //更新办件库信息
        if (StringUtils.isNotBlank(optionalOwnerQualification.get().getApplyNo())) {
            asyncService.approveOwnerQualification(optionalOwnerQualification.get().getApplyNo());
        }
        return new BaseResponse();
    }

    @Override
    public BaseResponse denyOwnerQualification(DenyOwnerQualificationDTO denyOwnerQualificationDTO) {
        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectByPrimaryKey(denyOwnerQualificationDTO.getId());
        if (!optionalOwnerQualification.isPresent()) {
            throw new ServiceException("未查询到相应用户信息");
        }
        if (!OwnerQualificationStatusEnum.RECONSIDERATION.getStatus().equals(optionalOwnerQualification.get().getApplyStatus())) {
            throw new ServiceException("当前状态不能进行该操作");
        }

        //更新用户资格信息
        Date now = new Date();
        UpdateStatementProvider updateStatement = SqlBuilder.update(ownerQualification)
                .set(ownerQualification.applyStatus).equalTo(OwnerQualificationStatusEnum.DENY.getStatus())
                .set(ownerQualification.reviewTime).equalTo(now)
                .set(ownerQualification.reason).equalTo(denyOwnerQualificationDTO.getReason())
                //注:此处需要将过期时间置空，来区分是允许补正的还是直接拒绝的
                .set(ownerQualification.expireTime).equalToNull()
                .set(ownerQualification.updatedTime).equalTo(now)
                .set(ownerQualification.updatedUserId).equalTo(denyOwnerQualificationDTO.getOperatorId())
                .set(ownerQualification.updatedUserName).equalTo(denyOwnerQualificationDTO.getOperatorName())
                .where(ownerQualification.id, isEqualTo(denyOwnerQualificationDTO.getId()))
                .and(ownerQualification.applyStatus, isEqualTo(OwnerQualificationStatusEnum.RECONSIDERATION.getStatus()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        ownerQualificationMapper.update(updateStatement);

        String content = "经查询，您的新能源购车资格申请未通过，暂不符合新能源汽车专用牌照申领条件，原因为：" + denyOwnerQualificationDTO.getReason() +";如有其他问题，可于工作日上午9:00-11:30，下午1:30-5:00咨询021-58812035。";
        MessageUtil.sendMessage(optionalOwnerQualification.get().getMobilePhone(), content);

        //保存操作日志
        SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                .userOperateTypeEnum(UserOperateTypeEnum.DENY_OWNER_QUALIFICATION)
                .reason(denyOwnerQualificationDTO.getReason())
                .vehicleId(denyOwnerQualificationDTO.getId())
                .operateUserId(denyOwnerQualificationDTO.getOperatorId())
                .operateUserName(denyOwnerQualificationDTO.getOperatorName())
                .build();
        userOperateLogService.saveOperateLog(saveOperateLogDTO);

        //更新办件库信息
        if (StringUtils.isNotBlank(optionalOwnerQualification.get().getApplyNo())) {
            asyncService.denyOwnerQualification(optionalOwnerQualification.get().getApplyNo(), denyOwnerQualificationDTO.getReason());
        }
        return new BaseResponse();
    }

    /**
     * 发短信测试
     */
    @Override
    public void sendMessageTest(String mobilephone) {
//        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectByPrimaryKey(id);
        String content = "您的购车资格申请核查数据有误，已帮您撤回，请重新提交申请";
        System.out.println("发送短信");
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        header.put("appkey","6YCa5Yqe5rWL6K+V");

        Map<String, String> params = new HashMap<>();
        params.put("phone",mobilephone);
        params.put("content",content);

        String response = RestTemplateUtil.post(ApiConst.Common.SEND_MESSAGE, header, params, String.class);
        logger.info("发送短信："+ mobilephone +",内容：" + content +" 调用结果:"+ response);
    }

    @Override
    public BaseResponse denyOwnerQualificationManualTest(DenyOwnerQualificationDTO denyOwnerQualificationDTO) {
        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectByPrimaryKey(denyOwnerQualificationDTO.getId());
        if (!optionalOwnerQualification.isPresent()) {
            throw new ServiceException("未查询到相应用户信息");
        }
        System.out.println("optionalOwnerQualification-----------------" + JSON.toJSONString(optionalOwnerQualification.get()));
        //更新用户资格信息
        Date now = new Date();
        UpdateStatementProvider updateStatement = SqlBuilder.update(ownerQualification)
                .set(ownerQualification.applyStatus).equalTo(OwnerQualificationStatusEnum.DENY.getStatus())
                .set(ownerQualification.reviewTime).equalTo(now)
                .set(ownerQualification.reason).equalTo(denyOwnerQualificationDTO.getReason())
                //注:此处需要将过期时间置空，来区分是允许补正的还是直接拒绝的
                .set(ownerQualification.expireTime).equalToNull()
                .set(ownerQualification.updatedTime).equalTo(now)
                .set(ownerQualification.updatedUserId).equalTo(denyOwnerQualificationDTO.getOperatorId())
                .set(ownerQualification.updatedUserName).equalTo(denyOwnerQualificationDTO.getOperatorName())
                .where(ownerQualification.id, isEqualTo(denyOwnerQualificationDTO.getId()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        ownerQualificationMapper.update(updateStatement);

        String content = "您好，《上海市鼓励购买和使用新能源汽车实施办法》（沪府办规〔2021〕3号）已于3月1日实施，因购车资格的要求有调整，请重新提交“购车资格查询”事项。";
        System.out.println("发送短信");
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        header.put("appkey","6YCa5Yqe5rWL6K+V");

        Map<String, String> params = new HashMap<>();
        params.put("phone",optionalOwnerQualification.get().getMobilePhone());
        params.put("content",content);

        String response = RestTemplateUtil.post(ApiConst.Common.SEND_MESSAGE, header, params, String.class);
        logger.info("发送短信："+ optionalOwnerQualification.get().getMobilePhone() +",内容：" + content +" 调用结果:"+ response);
        //保存操作日志
        SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                .userOperateTypeEnum(UserOperateTypeEnum.DENY_OWNER_QUALIFICATION)
                .reason(denyOwnerQualificationDTO.getReason())
                .vehicleId(denyOwnerQualificationDTO.getId())
                .operateUserId(denyOwnerQualificationDTO.getOperatorId())
                .operateUserName(denyOwnerQualificationDTO.getOperatorName())
                .build();
        userOperateLogService.saveOperateLog(saveOperateLogDTO);
        //更新办件库信息
        if (StringUtils.isNotBlank(optionalOwnerQualification.get().getApplyNo())) {
            libraryService.denyApply(optionalOwnerQualification.get().getApplyNo(), denyOwnerQualificationDTO.getReason());
        }
        return new BaseResponse();
    }

    /**
     * 保存私人用户购车资格申请
     * 处理私人用户的购车资格申请，包括重复申请检查、户籍验证、信息保存和审核启动
     *
     * @param savePrivateOwnerQualificationDTO 私人用户购车资格申请DTO
     * @return 操作结果响应
     * @throws ServiceException 当存在重复申请或户籍信息不符时抛出
     */
    @Override
    @Transactional
    public BaseResponse savePrivateOwnerQualification(SavePrivateOwnerQualificationDTO savePrivateOwnerQualificationDTO) {
        // 检查是否存在未完成的申请
        validateNoUnfinishedQualification(savePrivateOwnerQualificationDTO.getAuthType(), savePrivateOwnerQualificationDTO.getAuthId());

        // 验证户籍信息
        validateHouseholdRegistration(savePrivateOwnerQualificationDTO);

        // 创建并保存购车资格信息
        OwnerQualification qualification = createPrivateOwnerQualification(savePrivateOwnerQualificationDTO);
        saveOwnerQualificationInfo(qualification, savePrivateOwnerQualificationDTO.getCreatedUserId(), savePrivateOwnerQualificationDTO.getCreatedUserName());

        // 构建驾照信息并启动审核流程
        DriverLicenseDTO driverLicense = buildDriverLicenseFromDTO(savePrivateOwnerQualificationDTO);
        autoAssignQualificationReview(qualification, driverLicense);

        return new BaseResponse();
    }

    /**
     * 验证是否存在未完成的购车资格申请
     * @param authType 认证类型
     * @param authId 认证ID
     * @throws ServiceException 当存在未完成申请时抛出
     */
    private void validateNoUnfinishedQualification(Integer authType, String authId) {
        if (existUnfinishedQualification(authType, authId)) {
            throw new ServiceException("您已提交过申请，无法重复提交");
        }
    }

    /**
     * 验证户籍信息
     * 根据用户选择的户籍类型进行相应的验证
     *
     * @param dto 私人用户购车资格申请DTO
     * @throws ServiceException 当户籍信息验证失败时抛出
     */
    private void validateHouseholdRegistration(SavePrivateOwnerQualificationDTO dto) {
        if (dto.getHouseholdRegistrationType() == HOUSEHOLD_LOCAL) {
            // 验证上海户籍
            validateLocalHousehold(dto.getName(), dto.getAuthId());
        } else if (dto.getHouseholdRegistrationType() == HOUSEHOLD_EXTERNAL) {
            // 验证外省市居住证
            validateResidencePermit(dto.getName(), dto.getAuthId());
        }
    }

    /**
     * 验证上海本地户籍
     * @param name 用户姓名
     * @param authId 证件号
     * @throws ServiceException 当户籍信息不符时抛出
     */
    private void validateLocalHousehold(String name, String authId) {
        boolean isLocalHousehold = householdRegistrationService.isLocalHousehold(name, authId);
        if (!isLocalHousehold) {
            log.error("用户：{}，户籍信息填写异常", authId);
            throw new ServiceException("请确认户籍信息是否填写正确");
        }
    }

    /**
     * 验证居住证信息
     * @param name 用户姓名
     * @param authId 证件号
     * @throws ServiceException 当居住证信息不符时抛出
     */
    private void validateResidencePermit(String name, String authId) {
        boolean hasResidencePermit = residencePermitService.hasResidencePermit(name, authId);
        if (!hasResidencePermit) {
            throw new ServiceException("外省市（非港澳台及其他外籍人士）必须持有有效上海市居住证才可办理");
        }
    }

    /**
     * 创建私人用户购车资格对象
     * @param dto 私人用户购车资格申请DTO
     * @return 购车资格对象
     */
    private OwnerQualification createPrivateOwnerQualification(SavePrivateOwnerQualificationDTO dto) {
        OwnerQualification qualification = new OwnerQualification();
        qualification.setName(dto.getName());
        qualification.setAuthId(dto.getAuthId());
        qualification.setAuthType(dto.getAuthType());
        qualification.setHouseholdRegistrationType(dto.getHouseholdRegistrationType());
        qualification.setProperty(PROPERTY_PRIVATE); // 私人用户
        qualification.setIsMilitaryOfficer(NOT_MILITARY_OFFICER); // 非军官
        qualification.setVehicleModelType(dto.getVehicleModelType());
        return qualification;
    }

    /**
     * 从DTO构建驾照信息
     * @param dto 私人用户购车资格申请DTO
     * @return 驾照信息DTO
     */
    private DriverLicenseDTO buildDriverLicenseFromDTO(SavePrivateOwnerQualificationDTO dto) {
        return DriverLicenseDTO.builder()
                .driverLicenseCode(dto.getDriverLicenseCode())
                .driverLicenseIssuingOrganization(dto.getDriverLicenseIssuingOrganization())
                .driverLicenseIssuingPlace(dto.getDriverLicenseIssuingPlace())
                .driverFileNo(dto.getDriverFileNo())
                .build();
    }

    /**
     * 保存企业用户购车资格申请
     * 处理企业用户的购车资格申请，企业用户使用统一社会信用代码作为认证方式
     *
     * @param saveBusinessOwnerQualificationDTO 企业用户购车资格申请DTO
     * @return 操作结果响应
     * @throws ServiceException 当存在重复申请时抛出
     */
    @Override
    @Transactional
    public BaseResponse saveBusinessOwnerQualification(SaveBusinessOwnerQualificationDTO saveBusinessOwnerQualificationDTO) {
        Integer authType = AuthTypeEnum.CERTIFICATE_FOR_UNIFORM_SOCIAL_CREDIT_CODE.getType();

        // 检查是否存在未完成的申请
        validateNoUnfinishedQualification(authType, saveBusinessOwnerQualificationDTO.getAuthId());

        // 创建并保存企业购车资格信息
        OwnerQualification qualification = createBusinessOwnerQualification(saveBusinessOwnerQualificationDTO, authType);
        saveOwnerQualificationInfo(qualification, saveBusinessOwnerQualificationDTO.getCreatedUserId(), saveBusinessOwnerQualificationDTO.getCreatedUserName());

        // 启动审核流程（企业用户无需驾照信息）
        autoAssignQualificationReview(qualification);

        return new BaseResponse();
    }

    /**
     * 创建企业用户购车资格对象
     * @param dto 企业用户购车资格申请DTO
     * @param authType 认证类型（统一社会信用代码）
     * @return 购车资格对象
     */
    private OwnerQualification createBusinessOwnerQualification(SaveBusinessOwnerQualificationDTO dto, Integer authType) {
        OwnerQualification qualification = new OwnerQualification();
        qualification.setName(dto.getName());
        qualification.setAuthType(authType);
        qualification.setAuthId(dto.getAuthId());
        qualification.setProperty(PROPERTY_BUSINESS); // 企业用户
        qualification.setIsMilitaryOfficer(NOT_MILITARY_OFFICER); // 企业不涉及军官
        return qualification;
    }

    @Override
    @Transactional
    public BaseResponse saveForeignerQualificationInfo(SaveForeignerQualificationInfoDTO saveForeignerQualificationInfoDTO) {
        if (existUnfinishedQualification(saveForeignerQualificationInfoDTO.getAuthType(), saveForeignerQualificationInfoDTO.getAuthId())) {
            throw new ServiceException("您已提交过申请，无法重复提交");
        }
        //证件类型校验
        if (!Const.FOREIGNER_AUTH_TYPE.containsKey(saveForeignerQualificationInfoDTO.getAuthType())) {
            throw new ServiceException("该证件类型非港澳台或外籍证件，请在一网通办申请");
        }

        //保存意向用户信息
        OwnerQualification saveOwnerQualification = ConvertUtil.convert(saveForeignerQualificationInfoDTO, OwnerQualification.class);
        saveOwnerQualification.setIsMilitaryOfficer(0);
        saveOwnerQualification.setProperty(1);
        saveOwnerQualification.setHouseholdRegistrationType(2);
        saveOwnerQualification.setReconsiderationImg(saveForeignerQualificationInfoDTO.getCreatedUserOrgName());
        this.saveOwnerQualificationInfo(saveOwnerQualification, saveForeignerQualificationInfoDTO.getCreatedUserId(), saveForeignerQualificationInfoDTO.getCreatedUserName());

        //清除历史上传的扫描件
        fileService.clearFile(FileTypeEnum.QUALIFICATION_PROOF_COPY, saveOwnerQualification.getId(), saveForeignerQualificationInfoDTO.getCreatedUserId(), saveForeignerQualificationInfoDTO.getCreatedUserName());
        // 保存用户资格证明材料照扫描件信息
        fileService.saveFileInfo(FileTypeEnum.QUALIFICATION_PROOF_COPY, saveForeignerQualificationInfoDTO.getFile(), saveOwnerQualification.getId()
                , saveForeignerQualificationInfoDTO.getCreatedUserId(), saveForeignerQualificationInfoDTO.getCreatedUserName());

        //保存提交并受理办件
        DriverLicenseDTO driverLicense = DriverLicenseDTO.builder()
                .driverFileNo(saveForeignerQualificationInfoDTO.getDriverFileNo())
                .driverLicenseIssuingOrganization(saveForeignerQualificationInfoDTO.getDriverLicenseIssuingOrganization())
                .driverLicenseIssuingPlace(saveForeignerQualificationInfoDTO.getDriverLicenseIssuingPlace())
                .build();
        //自动分配核查信息
        autoAssignQualificationReview(saveOwnerQualification, driverLicense);
        return new BaseResponse();
    }

    @Override
    public BaseResponse saveMilitaryQualificationInfo(SaveMilitaryQualificationInfoDTO saveMilitaryQualificationInfoDTO) {
        if (existUnfinishedQualification(saveMilitaryQualificationInfoDTO.getAuthType(), saveMilitaryQualificationInfoDTO.getAuthId())) {
            throw new ServiceException("您已提交过申请，无法重复提交");
        }
        //保存意向用户信息
        OwnerQualification saveOwnerQualification = ConvertUtil.convert(saveMilitaryQualificationInfoDTO, OwnerQualification.class);
        saveOwnerQualification.setIsMilitaryOfficer(1);
        saveOwnerQualification.setProperty(1);
        saveOwnerQualification.setReconsiderationImg(saveMilitaryQualificationInfoDTO.getCreatedUserOrgName());
        this.saveOwnerQualificationInfo(saveOwnerQualification, saveMilitaryQualificationInfoDTO.getCreatedUserId(), saveMilitaryQualificationInfoDTO.getCreatedUserName());

        //清除历史上传的扫描件
        fileService.clearFile(FileTypeEnum.MILITARY_OFFICER_COPY, saveOwnerQualification.getId(), saveMilitaryQualificationInfoDTO.getCreatedUserId(), saveMilitaryQualificationInfoDTO.getCreatedUserName());
        // 保存军官证件照扫描件信息
        fileService.saveFileInfo(FileTypeEnum.MILITARY_OFFICER_COPY, saveMilitaryQualificationInfoDTO.getFile(), saveOwnerQualification.getId()
                , saveMilitaryQualificationInfoDTO.getCreatedUserId(), saveMilitaryQualificationInfoDTO.getCreatedUserName());

        //保存提交并受理办件
        DriverLicenseDTO driverLicense = DriverLicenseDTO.builder()
                .driverLicenseCode(saveMilitaryQualificationInfoDTO.getDriverLicenseCode())
                .driverLicenseIssuingOrganization(saveMilitaryQualificationInfoDTO.getDriverLicenseIssuingOrganization())
                .driverLicenseIssuingPlace(saveMilitaryQualificationInfoDTO.getDriverLicenseIssuingPlace())
                .build();
        //自动分配核查信息
        autoAssignQualificationReview(saveOwnerQualification, driverLicense);
        return new BaseResponse();
    }

    @Override
    public BaseResponse invalidOwnerQualification(InvalidOwnerQualificationDTO invalidOwnerQualificationDTO) {
        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectByPrimaryKey(invalidOwnerQualificationDTO.getId());
        if (!optionalOwnerQualification.isPresent()) {
            throw new ServiceException("未查询到相应用户信息");
        }
        if (optionalOwnerQualification.get().getExpireTime() == null) {
            throw new ServiceException("该数据无法失效");
        }
        Date now = new Date();
        if (optionalOwnerQualification.get().getExpireTime().compareTo(now) <= 0) {
            throw new ServiceException("该资格信息已过期，无需失效");
        }

        //更新用户购车资格有效期，提前结束
        UpdateStatementProvider updateStatement = SqlBuilder.update(ownerQualification)
                .set(ownerQualification.expireTime).equalTo(now)
                .set(ownerQualification.updatedUserId).equalTo(invalidOwnerQualificationDTO.getOperatorId())
                .set(ownerQualification.updatedUserName).equalTo(invalidOwnerQualificationDTO.getOperatorName())
                .set(ownerQualification.updatedTime).equalTo(now)
                .where(ownerQualification.id, isEqualTo(invalidOwnerQualificationDTO.getId()))
                .and(ownerQualification.expireTime, isNotNull())
                .and(ownerQualification.expireTime, isGreaterThan(now))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        ownerQualificationMapper.update(updateStatement);

        //保存操作日志
        SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                .userOperateTypeEnum(UserOperateTypeEnum.INVALID_OWNER_QUALIFICATION)
                .reason(invalidOwnerQualificationDTO.getReason())
                .ownerQualificationId(invalidOwnerQualificationDTO.getId())
                .operateUserId(invalidOwnerQualificationDTO.getOperatorId())
                .operateUserName(invalidOwnerQualificationDTO.getOperatorName())
                .build();
        userOperateLogService.saveOperateLog(saveOperateLogDTO);
        return new BaseResponse();
    }

    @Override
    public BaseResponse batchInvalidOwnerQualification(BatchInvalidOwnerQualificationDTO batchInvalidOwnerQualificationDTO) {
        final List<OwnerQualification> ownerQualifications = queryOwnerQualifications(batchInvalidOwnerQualificationDTO.getIds());
        for(OwnerQualification qualification : ownerQualifications){
            if (qualification.getExpireTime() == null) {
                continue;
            }
            Date now = new Date();
            if (qualification.getExpireTime().compareTo(now) <= 0) {
                continue;
            }
            //更新用户购车资格有效期，提前结束
            UpdateStatementProvider updateStatement = SqlBuilder.update(ownerQualification)
                    .set(ownerQualification.expireTime).equalTo(now)
                    .set(ownerQualification.updatedUserId).equalTo(batchInvalidOwnerQualificationDTO.getOperatorId())
                    .set(ownerQualification.updatedUserName).equalTo(batchInvalidOwnerQualificationDTO.getOperatorName())
                    .set(ownerQualification.updatedTime).equalTo(now)
                    .where(ownerQualification.id, isEqualTo(qualification.getId()))
                    .and(ownerQualification.expireTime, isNotNull())
                    .and(ownerQualification.expireTime, isGreaterThan(now))
                    .build()
                    .render(RenderingStrategies.MYBATIS3);
            ownerQualificationMapper.update(updateStatement);

            //保存操作日志
            SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                    .userOperateTypeEnum(UserOperateTypeEnum.INVALID_OWNER_QUALIFICATION)
                    .reason(batchInvalidOwnerQualificationDTO.getReason())
                    .ownerQualificationId(qualification.getId())
                    .operateUserId(batchInvalidOwnerQualificationDTO.getOperatorId())
                    .operateUserName(batchInvalidOwnerQualificationDTO.getOperatorName())
                    .build();
            userOperateLogService.saveOperateLog(saveOperateLogDTO);
        }
        return new BaseResponse();
    }

    /**
     * 判断当前用户是否资格核查通过（有效期内）
     *
     * @param authTypeValue 证件类型
     * @param authIdValue   证件号码
     * @return OwnerQualification
     */
    @Override
    public OwnerQualification queryApprovedQualification(Integer authTypeValue, String authIdValue) {
        //查询核查通过且未过期的意向用户记录
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where()
                .and(ownerQualification.authType, isEqualTo(authTypeValue))
                .and(ownerQualification.authId, isEqualTo(authIdValue))
                .and(ownerQualification.applyStatus, isEqualTo(OwnerQualificationStatusEnum.APPROVE.getStatus()))
                .and(ownerQualification.expireTime, isGreaterThan(new Date()))
                .and(ownerQualification.qualificationType, isEqualTo(OwnerQualificationTypeEnum.NORMAL.getType()))
                .and(ownerQualification.status,isEqualTo(1))
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        final Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectOne(selectStatement);
        return optionalOwnerQualification.orElse(null);
    }

    @Override
    public OwnerQualification queryLastApprovedQualification(Integer authTypeValue, String authIdValue) {
        //查询最近一条核查通过意向用户记录
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where()
                .and(ownerQualification.authType, isEqualToWhenPresent(authTypeValue))
                .and(ownerQualification.authId, isEqualTo(authIdValue))
                .and(ownerQualification.applyStatus, isEqualTo(OwnerQualificationStatusEnum.APPROVE.getStatus()))
                .and(ownerQualification.qualificationType, isEqualTo(OwnerQualificationTypeEnum.NORMAL.getType()))
                .and(ownerQualification.status,isEqualTo(1))
                .orderBy(ownerQualification.expireTime.descending())
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        final Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectOne(selectStatement);
        return optionalOwnerQualification.orElse(null);
    }

    @Override
    public OwnerIdentityInfo queryApprovedQualificationHistory(String authKind, String authId) {
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerIdentityInfo.allColumns())
                .from(ownerIdentityInfo)
                .where(ownerIdentityInfo.authIdentityId, isEqualTo(authId))
                .and(ownerIdentityInfo.authIdentityKind, isEqualTo(authKind))
                .and(ownerIdentityInfo.status, isEqualTo(3))
                .and(ownerIdentityInfo.expireTime, isGreaterThanOrEqualTo(new Date()))
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        final Optional<OwnerIdentityInfo> ownerIdentityInfo = ownerIdentityInfoMapper.selectOne(selectStatement);
        return ownerIdentityInfo.orElse(null);
    }

    /**
     * 处理遗漏的购车资格申请办件
     * 查询待预审状态的办件，根据来源不同进行相应处理
     *
     * @return 处理结果响应
     */
    @Override
    public BaseResponse dealOmitQualificationApply() {
        // 查询待预审的购车资格申请办件
        List<ItemApplyListDTO> pendingApplyList = queryPendingQualificationApplies();
        if (pendingApplyList.isEmpty()) {
            return new BaseResponse("无待预审办件", ResultCode.SUCCESS);
        }

        // 逐个处理待预审办件
        for (ItemApplyListDTO itemApply : pendingApplyList) {
            processOmitQualificationApply(itemApply);
        }

        return new BaseResponse();
    }

    /**
     * 查询待预审的购车资格申请办件列表
     *
     * @return 待预审办件列表，如果无数据则返回空列表
     */
    private List<ItemApplyListDTO> queryPendingQualificationApplies() {
        List<String> itemCodes = new ArrayList<String>() {{
            add(LibraryApplyItemEnum.OWNER_QUALIFICATION_APPLY.getItemCode());
        }};

        QueryItemsApplyListResponse response = libraryService.queryItemsApplyList(itemCodes, "待预审", 100);
        if (response == null || response.getData() == null || response.getData().getApply() == null) {
            return new ArrayList<>();
        }

        return response.getData().getApply();
    }

    /**
     * 处理单个遗漏的购车资格申请办件
     * 根据办件来源（二手车平台或一网通办）进行不同的处理逻辑
     *
     * @param itemApply 待处理的办件信息
     */
    private void processOmitQualificationApply(ItemApplyListDTO itemApply) {
        Map<String, Object> info = itemApply.getInfo();
        if (info == null) {
            return;
        }

        String origin = (String) info.get("origin");
        if (StringUtils.compare("extracme", origin) == 0) {
            // 处理来自二手车平台的申请
            processExtracmeOriginApply(itemApply);
        } else {
            // 处理来自一网通办的申请
            processOneNetOriginApply(itemApply, info);
        }
    }

    /**
     * 处理来自二手车平台的购车资格申请
     * 由于二手车平台数据直接存储到数据库，需要根据办件编号查询相关信息并同步状态
     *
     * @param itemApply 办件信息
     */
    private void processExtracmeOriginApply(ItemApplyListDTO itemApply) {
        // 根据办件编号查询已存储的购车资格信息
        OwnerQualification existingQualification = queryOwnerQualificationByApplyNo(itemApply.getApplyNo());

        if (existingQualification != null) {
            // 受理办件并同步状态
            libraryService.acceptApply(itemApply.getApplyNo());
            syncQualificationStatusToLibrary(itemApply.getApplyNo(), existingQualification);
        } else {
            // 数据异常，拒绝受理
            libraryService.refuseAcceptApply(itemApply.getApplyNo(),
                "您提交的信息存在异常，请确保填写信息正确后重新提交");
        }
    }

    /**
     * 同步购车资格状态到办件库
     * 根据购车资格的审核状态，更新办件库中的对应状态
     *
     * @param applyNo 办件编号
     * @param qualification 购车资格信息
     */
    private void syncQualificationStatusToLibrary(String applyNo, OwnerQualification qualification) {
        if (OwnerQualificationStatusEnum.APPROVE.getStatus().equals(qualification.getApplyStatus())) {
            libraryService.approveApply(applyNo);
        } else if (OwnerQualificationStatusEnum.DENY.getStatus().equals(qualification.getApplyStatus())) {
            libraryService.denyApply(applyNo, qualification.getReason());
        }
    }

    /**
     * 处理来自一网通办的购车资格申请
     * 解析办件信息，获取申请人基础信息，并启动购车资格审核流程
     *
     * @param itemApply 办件信息
     * @param info 办件详细信息
     */
    private void processOneNetOriginApply(ItemApplyListDTO itemApply, Map<String, Object> info) {
        try {
            // 解析办件信息为购车资格申请DTO
            OwnerQualificationLibraryInfoDTO libraryInfo = parseLibraryInfo(info);

            // 补充申请经办人证件信息
            supplementLicenseInfo(itemApply.getApplyNo(), libraryInfo);

            logger.info("处理办件" + itemApply.getApplyNo() + ":" + JSON.toJSONString(libraryInfo));

            // 使用分布式锁防止重复处理
            processWithDistributedLock(itemApply, libraryInfo);

        } catch (Exception e) {
            logger.error("处理一网通办办件异常: " + itemApply.getApplyNo(), e);
        }
    }

    /**
     * 解析办件库信息为购车资格申请DTO
     *
     * @param info 办件详细信息
     * @return 购车资格申请DTO
     */
    private OwnerQualificationLibraryInfoDTO parseLibraryInfo(Map<String, Object> info) {
        return JSON.parseObject(JSON.toJSONString(info), OwnerQualificationLibraryInfoDTO.class);
    }

    /**
     * 补充申请经办人证件信息
     * 从办件基础信息中获取经办人的证件号和证件类型
     *
     * @param applyNo 办件编号
     * @param libraryInfo 购车资格申请信息
     */
    private void supplementLicenseInfo(String applyNo, OwnerQualificationLibraryInfoDTO libraryInfo) {
        final GetApplyBaseResponse applyBase = libraryService.getApplyBase(applyNo);
        if (applyBase != null && applyBase.getData() != null && !applyBase.getData().isEmpty()) {
            libraryInfo.setLicenseNo(applyBase.getData().get(0).getLicenseNo());
            libraryInfo.setLicenseType(applyBase.getData().get(0).getLicenseType());
        } else {
            log.warn("未查询到办件基础信息:" + applyNo);
        }
    }

    /**
     * 使用分布式锁处理购车资格申请
     * 防止同一办件被多个线程同时处理
     *
     * @param itemApply 办件信息
     * @param libraryInfo 购车资格申请信息
     */
    private void processWithDistributedLock(ItemApplyListDTO itemApply, OwnerQualificationLibraryInfoDTO libraryInfo) {
        String lockKey = itemApply.getApplyNo();
        try {
            // 尝试获取30秒的分布式锁
            if (MemcachedUtil.add(lockKey, "value", 30)) {
                // 处理用户购车资格申请
                acceptQualificationApply(itemApply.getApplyNo(), itemApply.getParentApplyNo(), libraryInfo);
            } else {
                logger.error("已有线程正在处理办件:" + lockKey);
            }
        } finally {
            // 确保释放锁
            MemcachedUtil.delete(lockKey);
        }
    }

    @Override
    public void dealQualificationApply(String applyNo, String uapplyNo) {
        GetApplyBaseResponse applyBaseResponse = null;
        for (int i = 0; i < 10; i++) {
            try {
                applyBaseResponse = libraryService.getApplyBase(applyNo);
                break;
            } catch (Exception e) {
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException ex) {
                    ex.printStackTrace();
                }
                continue;
            }
        }

        if (applyBaseResponse == null || applyBaseResponse.getData() == null || applyBaseResponse.getData().size() == 0) {
            return;
        }
        final GetApplyBaseDTO applyBaseInfo = applyBaseResponse.getData().get(0);
        if (StringUtils.compare(applyBaseInfo.getStatus(), "待预审") == 0) {
            OwnerQualificationLibraryInfoDTO libraryInfo = new OwnerQualificationLibraryInfoDTO();
            try {
                //将办件库的Map数据转化成指定类进行处理
                BeanUtils.populate(libraryInfo, applyBaseInfo.getInfo());
                //申请经办人证件编号
                libraryInfo.setLicenseNo(applyBaseInfo.getLicenseNo());
                //申请经办人证件类型
                libraryInfo.setLicenseType(applyBaseInfo.getLicenseType());

                acceptQualificationApply(applyBaseInfo.getApplyNo(), uapplyNo, libraryInfo);
            } catch (Exception e) {
                //终止办件
                logger.error(e.getMessage(),e);
            }
        }
    }

    @Override
    public void dealSupplementQualificationApply(String applyNo) {
        //查询相关意向用户信息
        final OwnerQualification ownerQualification = queryOwnerQualificationByApplyNo(applyNo);
        if (ownerQualification == null) {
            throw new ServiceException("未查询到相关用户资格信息");
        }
        if (!OwnerQualificationStatusEnum.DENY.getStatus().equals(ownerQualification.getApplyStatus())) {
            throw new ServiceException("当前状态不能进行该操作");
        }
        //获取补正材料基本信息
        final GetApplyStuffResponse applyStuffResponse = libraryService.getApplyStuff(applyNo);
        if (applyStuffResponse == null || applyStuffResponse.getData() == null || applyStuffResponse.getData().size() == 0) {
            //TODO 是否应该拒绝该办件？？
            return;
        }

        //清空之前上传的图片
        fileService.clearFile(FileTypeEnum.QUALIFICATION_PROOF_COPY, ownerQualification.getId(), "-1", "system");

        List<GetApplyStuffDTO> applyStuffList = applyStuffResponse.getData();
        for (GetApplyStuffDTO applyStuff : applyStuffList) {
            if (StringUtils.isNotBlank(applyStuff.getStuffId())) {
                //下载文件
                final ByteArrayResource byteArrayResource = libraryService.downloadStuff(applyStuff.getStuffId());
                String fileExtension = StringUtils.substringAfterLast(applyStuff.getFilename(), ".");

                //保存文件
                fileService.saveFileInfo(FileTypeEnum.QUALIFICATION_PROOF_COPY, fileExtension, byteArrayResource,
                        ownerQualification.getId(), Const.ADMIN_ORG_ID, Const.ADMIN_ID, Const.ADMIN_NAME);
            }
        }
        //将办件状态更新为复核
        reconsiderQualification(ownerQualification.getId(), Const.ADMIN_ID, Const.ADMIN_NAME, "一网通办");

        //受理办件
        libraryService.acceptApply(applyNo);
        //更新指南
        libraryService.changeStatus(applyNo, AnnouncementEnum.QUALIFICATION_ACCEPTED);
    }

    @Override
    public BaseResponse reconsiderOwnerQualification(ReconsiderOwnerQualificationDTO reconsiderOwnerQualificationDTO) {
        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectByPrimaryKey(reconsiderOwnerQualificationDTO.getOwnerQualificationId());
        if (!optionalOwnerQualification.isPresent()) {
            throw new ServiceException("未查询到相应购车资格信息");
        }
        if (!OwnerQualificationStatusEnum.DENY.getStatus().equals(optionalOwnerQualification.get().getApplyStatus())) {
            throw new ServiceException("当前状态不能进行该操作");
        }

        //  可以简化成针对状态+有效期的判断
        //复核条件判断
        if (!"test".equals(SpringContextUtil.getActiveProfile()) && !"dev".equals(SpringContextUtil.getActiveProfile())) {
            if (!canQualificationReconsider(optionalOwnerQualification.get())) {
                throw new ServiceException("当前状态不能进行复核");
            }
        }


        // 判断是否有照片，是否之前复核过，只能复核一次
        List<FileInfoDTO> fileInfo = fileService.getFileInfo(FileTypeEnum.QUALIFICATION_PROOF_COPY, optionalOwnerQualification.get().getId());
        if (!fileInfo.isEmpty()) {
            throw new ServiceException("该记录已复核过，不可多次复核，请重新创建并提交申请");
        }

        //清空之前上传的图片
        fileService.clearFile(FileTypeEnum.QUALIFICATION_PROOF_COPY, optionalOwnerQualification.get().getId(), reconsiderOwnerQualificationDTO.getUpdatedUserId(), reconsiderOwnerQualificationDTO.getUpdatedUserName());
        //保存上传的文件
        fileService.saveFileInfo(FileTypeEnum.QUALIFICATION_PROOF_COPY, reconsiderOwnerQualificationDTO.getFiles(), reconsiderOwnerQualificationDTO.getOwnerQualificationId(),
                reconsiderOwnerQualificationDTO.getUpdatedUserId(), reconsiderOwnerQualificationDTO.getUpdatedUserName());

        //将办件状态更新为复核
        reconsiderQualification(optionalOwnerQualification.get().getId(), Const.ADMIN_ID, Const.ADMIN_NAME,"二手车平台");
        if (StringUtils.isNotBlank(optionalOwnerQualification.get().getApplyNo())) {
            //受理办件
            libraryService.acceptApply(optionalOwnerQualification.get().getApplyNo());
            //更新指南
            libraryService.changeStatus(optionalOwnerQualification.get().getApplyNo(), AnnouncementEnum.QUALIFICATION_ACCEPTED);
        }
        return new BaseResponse();
    }

    @Override
    public BaseResponse dealExpireReconsiderQualification() {
        //查询被拒绝且已经过期的用户资格
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where(ownerQualification.applyStatus, isEqualTo(OwnerQualificationStatusEnum.DENY.getStatus()))
                .and(ownerQualification.expireTime, isLessThanOrEqualTo(new Date()))
                .and(ownerQualification.qualificationType, isEqualTo(OwnerQualificationTypeEnum.NORMAL.getType()))
                .and(ownerQualification.status,isEqualTo(1))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        final List<OwnerQualification> ownerQualificationList = ownerQualificationMapper.selectMany(selectStatement);
        for (OwnerQualification expiredOwnerQualification : ownerQualificationList) {
            //如果有办件编号
            if (StringUtils.isNotBlank(expiredOwnerQualification.getApplyNo())) {
                //拒绝办件
                libraryService.acceptApply(expiredOwnerQualification.getApplyNo());

                libraryService.denyApply(expiredOwnerQualification.getApplyNo(), "未在规定时间内上传复核材料");

                libraryService.changeStatus(expiredOwnerQualification.getApplyNo(), AnnouncementEnum.QUALIFICATION_DENY);
            }

            //更新用户资格信息
            UpdateStatementProvider updateStatement = SqlBuilder.update(ownerQualification)
                    .set(ownerQualification.updatedUserId).equalTo(Const.ADMIN_ID)
                    .set(ownerQualification.updatedUserName).equalTo(Const.SCHEDULE)
                    .set(ownerQualification.expireTime).equalToNull()
                    .set(ownerQualification.updatedTime).equalTo(new Date())
                    .where(ownerQualification.id, isEqualTo(expiredOwnerQualification.getId()))
                    .build()
                    .render(RenderingStrategies.MYBATIS3);
            ownerQualificationMapper.update(updateStatement);
        }
        return new BaseResponse();
    }

    @Override
    public AbnormalQualificationResponse searchAbnormalQualification(SearchAbnormalQualificationDTO searchAbnormalQualificationDTO) {

        AbnormalQualificationResponse result = new AbnormalQualificationResponse();
        //查询信用异常记录
        //考虑到信用记录理论上一般会及时查询反馈，且之后会每间隔2小时重复查询，所以未出结果信用数据都为异常数据
        SelectStatementProvider selectStatement = SqlBuilder.select(
                ownerQualificationReviewDetail.id,
                ownerQualification.id,
                ownerQualification.name,
                ownerQualification.authId,
                ownerQualification.createdTime,
                ownerQualificationReviewDetail.remark)
                .from(ownerQualificationReviewDetail)
                .leftJoin(ownerQualification)
                .on(ownerQualification.id, equalTo(ownerQualificationReviewDetail.ownerQulificationId))
                .where(ownerQualificationReviewDetail.reviewStatus, isEqualTo(0))
                .and(ownerQualificationReviewDetail.reviewType, isEqualTo(ReviewTypeEnum.CREDIT.getValue()))
                .limit(searchAbnormalQualificationDTO.getPageSize())
                .offset((searchAbnormalQualificationDTO.getPageNum() - 1) * searchAbnormalQualificationDTO.getPageSize())
                .build()
                .render(RenderingStrategies.MYBATIS3);

        final List<AbnormalCreditQualificationDTO> abnormalCreditQualification = ownerQualificationMapper.searchAbnormalCreidtQualification(selectStatement);

        SelectStatementProvider countStatement = SqlBuilder.select(count())
                .from(ownerQualificationReviewDetail)
                .where(ownerQualificationReviewDetail.reviewStatus, isEqualTo(0))
                .and(ownerQualificationReviewDetail.reviewType, isEqualTo(ReviewTypeEnum.CREDIT.getValue()))
                .build()
                .render(RenderingStrategies.MYBATIS3);

        long count = ownerQualificationMapper.count(countStatement);
        result.setAbnormalCreditQualification(new PageInfoBO<>(count,abnormalCreditQualification));

        //TODO 查询社保异常记录（目前木有）

        //TODO 查询公安异常记录
        return result;
    }


    @Override
    public OwnerSupplementInfoDTO queryOwnerSupplementInfo(String applyNo) {
        OwnerQualification qualification = queryOwnerQualificationByApplyNo(applyNo);
        if (qualification == null) {
            throw new ServiceException("未查询到相关办件信息");
        }
        //用户资格信息
        OwnerQualificationInfoDTO qualificationInfo = new OwnerQualificationInfoDTO();
        qualificationInfo.setName(qualification.getName());
        qualificationInfo.setAuthType(qualification.getAuthType());
        qualificationInfo.setAuthId(qualification.getAuthId());
        qualificationInfo.setProperty(qualification.getProperty());
        qualificationInfo.setQualificationReviewStatus(qualification.getApplyStatus());
        qualificationInfo.setQualificationReviewReason(qualification.getReason());
        qualificationInfo.setQualificationSubmitTime(qualification.getApplyTime());

        //用户公安驾照信息
        OwnerTrafficQualification trafficQualification = ownerTrafficQualificationService.queryOwnerTrafficQualification(qualification.getId());
        if (trafficQualification != null) {
            qualificationInfo.setDriverLicenseCode(trafficQualification.getDriverLicenseCode());
            qualificationInfo.setDriverLicenseIssuingOrganization(trafficQualification.getDriverLicenseIssuingOrganization());
            qualificationInfo.setDriverLicenseIssuingPlace(trafficQualification.getDriverLicenseIssuingPlace());
        }

        OwnerSupplementInfoDTO ownerSupplementInfo = new OwnerSupplementInfoDTO();
        ownerSupplementInfo.setOwnerQualificationInfo(qualificationInfo);
        return ownerSupplementInfo;
    }

    /**
     * 处理未完成的购车资格审核
     * 重新触发各个审核服务对待审核状态的购车资格进行核查
     *
     * @return 处理结果响应
     */
    @Override
    public BaseResponse dealPendingQualificationReview() {
        // 处理各类待审核的购车资格
        processPendingSocialReview();
        processPendingCreditReview();
        processPendingTrafficReview();
        processPendingResidencePermitReview();
        processPendingTrafficCommitteeReview();
        processPendingSpecialTalentReview();
        processPendingExchangeLimitReview();

        return new BaseResponse();
    }

    /**
     * 处理待审核的社保信息
     * 查询未核查完成的社保审核记录，重新触发社保核查
     */
    private void processPendingSocialReview() {
        List<OwnerQualificationReviewDetail> socialReviewList = socialService.queryPendingReview();
        List<Long> socialQualificationIds = extractQualificationIds(socialReviewList);
        List<OwnerQualification> socialQualificationList = queryOwnerQualifications(socialQualificationIds);

        for (OwnerQualification qualification : socialQualificationList) {
            // 重新核查社保信息
            newSocialService.review(qualification.getId(), qualification.getName(),
                qualification.getAuthType(), qualification.getAuthId());
        }
    }

    /**
     * 处理待审核的信用信息
     * 查询未核查完成的信用审核记录，重新触发信用核查
     */
    private void processPendingCreditReview() {
        List<OwnerQualificationReviewDetail> creditReviewList = creditService.queryPendingReview();
        List<Long> creditQualificationIds = extractQualificationIds(creditReviewList);
        List<OwnerQualification> creditQualificationList = queryOwnerQualifications(creditQualificationIds);

        for (OwnerQualification qualification : creditQualificationList) {
            // 重新核查信用信息
            creditService.review(qualification.getId(), qualification.getProperty(),
                qualification.getName(), qualification.getAuthId(), true);
        }
    }

    /**
     * 处理待审核的公安交通信息
     * 查询未核查完成的公安审核记录，重新触发公安核查
     */
    private void processPendingTrafficReview() {
        List<OwnerQualificationReviewDetail> trafficReviewList = driverService.queryPendingReview();
        List<Long> trafficQualificationIds = extractQualificationIds(trafficReviewList);
        List<OwnerQualification> trafficQualificationList = queryOwnerQualifications(trafficQualificationIds);

        for (OwnerQualification qualification : trafficQualificationList) {
            // 重新核查公安信息
            driverService.review(qualification.getId());
        }
    }

    /**
     * 处理待审核的居住证信息
     * 查询未核查完成的居住证审核记录，重新触发居住证核查
     */
    private void processPendingResidencePermitReview() {
        List<OwnerQualificationReviewDetail> residencePermitReviewList = residencePermitService.queryPendingReview();
        List<Long> residencePermitIds = extractQualificationIds(residencePermitReviewList);
        List<OwnerQualification> residencePermitQualificationList = queryOwnerQualifications(residencePermitIds);

        for (OwnerQualification qualification : residencePermitQualificationList) {
            // 重新核查居住证信息
            residencePermitService.review(qualification.getId(), qualification.getName(),
                qualification.getAuthId(), true);
        }
    }

    /**
     * 处理待审核的交通委大牌额度信息
     * 查询未核查完成的交通委审核记录，重新触发交通委核查
     */
    private void processPendingTrafficCommitteeReview() {
        List<OwnerQualificationReviewDetail> trafficCommitteeReviewList = trafficCommitteeService.queryPendingLicenseLimitReview();
        List<Long> trafficCommitteeIds = extractQualificationIds(trafficCommitteeReviewList);
        List<OwnerQualification> trafficCommitteeQualificationList = queryOwnerQualifications(trafficCommitteeIds);

        for (OwnerQualification qualification : trafficCommitteeQualificationList) {
            // 重新核查交委信息
            trafficCommitteeService.reviewLicenseLimit(qualification.getId(), true);
        }
    }

    /**
     * 处理待审核的特殊人才信息
     * 查询未核查完成的特殊人才审核记录，重新触发特殊人才核查
     */
    private void processPendingSpecialTalentReview() {
        List<OwnerQualificationReviewDetail> specialTrafficReviewList = driverService.querySpecialPendingReview();
        List<Long> specialTrafficQualificationIds = specialTrafficReviewList.stream()
            .map(OwnerQualificationReviewDetail::getOwnerQulificationId)
            .distinct()
            .collect(Collectors.toList());
        List<OwnerQualification> specialTrafficQualificationList = queryOwnerQualifications(specialTrafficQualificationIds);

        for (OwnerQualification qualification : specialTrafficQualificationList) {
            // 重新核查特殊人才信息
            driverService.review(qualification.getId(), true);
        }
    }

    /**
     * 处理待审核的交通委以旧换新额度信息
     * 查询未核查完成的以旧换新额度审核记录，重新触发额度核查
     */
    private void processPendingExchangeLimitReview() {
        List<OwnerQualificationReviewDetail> exchangeLimitReviewList = trafficCommitteeService.queryPendingExchangeLimitReview();
        List<Long> exchangeLimitQualificationIds = extractQualificationIds(exchangeLimitReviewList);

        for (Long qualificationId : exchangeLimitQualificationIds) {
            // 重新核查交通委以旧换新额度
            trafficCommitteeService.reviewExchangeLimit(qualificationId);
        }
    }

    /**
     * 从审核详情列表中提取购车资格ID列表
     * 通用工具方法，避免重复代码
     *
     * @param reviewDetailList 审核详情列表
     * @return 购车资格ID列表
     */
    private List<Long> extractQualificationIds(List<OwnerQualificationReviewDetail> reviewDetailList) {
        return reviewDetailList.stream()
            .map(OwnerQualificationReviewDetail::getOwnerQulificationId)
            .collect(Collectors.toList());
    }

    @Override
    public boolean hasReview(Long ownerQualificationId, Integer reviewType) {
        SelectStatementProvider render = countFrom(ownerQualificationReviewDetail)
                .where(ownerQualificationReviewDetail.ownerQulificationId, isEqualTo(ownerQualificationId))
                .and(ownerQualificationReviewDetail.status, isEqualTo(1))
                .and(ownerQualificationReviewDetail.reviewType, isEqualTo(reviewType))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        long count = ownerQualificationReviewDetailMapper.count(render);
        if (count > 0) {
            return true;
        }
        return false;
    }

    @Override
    public CurrentOwnerQualificationDTO queryCurrentOwnerQualification(Integer authType, String authId) {
        CurrentOwnerQualificationDTO currentOwnerQualification;
        //查询最近一条意向用户记录
        SelectStatementProvider selectStatement = select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where()
                .and(ownerQualification.authId, isEqualTo(authId))
                .and(ownerQualification.authType, isEqualTo(authType))
                .and(ownerQualification.qualificationType, isEqualTo(OwnerQualificationTypeEnum.NORMAL.getType()))
                .and(ownerQualification.status,isEqualTo(1))
                .orderBy(ownerQualification.id.descending())
                .limit(1)
                .build().render(RenderingStrategies.MYBATIS3);
        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectOne(selectStatement);
        if(optionalOwnerQualification.isPresent()){
            currentOwnerQualification = ConvertUtil.convert(optionalOwnerQualification.get(), CurrentOwnerQualificationDTO.class);
        }else{
            currentOwnerQualification = new CurrentOwnerQualificationDTO();
        }
        return currentOwnerQualification;
    }

    @Override
    public void cancelReconsiderOwnerQualification(Long id, String reason, String operatorId, String operatorName){
        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectByPrimaryKey(id);
        if (!optionalOwnerQualification.isPresent()) {
            throw new ServiceException("未查询到相应购车资格信息");
        }
        if(!OwnerQualificationStatusEnum.DENY.getStatus().equals(optionalOwnerQualification.get().getApplyStatus())){
            throw new ServiceException("当前购车资格状态无法取消复核");
        }
        if (optionalOwnerQualification.get().getExpireTime() == null) {
            throw new ServiceException("该购车资格信息已取消复核");
        }
        Date now = new Date();
        if (optionalOwnerQualification.get().getExpireTime().compareTo(now) <= 0) {
            throw new ServiceException("该购车资格信息已过期，无需失效");
        }

        //更新用户购车资格有效期，提前结束
        UpdateStatementProvider updateStatement = SqlBuilder.update(ownerQualification)
                .set(ownerQualification.expireTime).equalTo(now)
                .set(ownerQualification.updatedUserId).equalTo(operatorId)
                .set(ownerQualification.updatedUserName).equalTo(operatorName)
                .set(ownerQualification.updatedTime).equalTo(now)
                .where(ownerQualification.id, isEqualTo(id))
                .and(ownerQualification.expireTime, isNotNull())
                .and(ownerQualification.expireTime, isGreaterThan(now))
                .and(ownerQualification.applyStatus, isEqualTo(OwnerQualificationStatusEnum.DENY.getStatus()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        ownerQualificationMapper.update(updateStatement);

        //保存操作日志
        SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                .userOperateTypeEnum(UserOperateTypeEnum.CANCEL_RECONSIDER_OWNER_QUALIFICATION)
                .reason(reason)
                .ownerQualificationId(id)
                .operateUserId(operatorId)
                .operateUserName(operatorName)
                .build();
        userOperateLogService.saveOperateLog(saveOperateLogDTO);
    }

    @Override
    public QueryOwnerBaseQualificationResponse queryOwnerBaseQualification(String name, Integer authType, String authId) {
        QueryOwnerBaseQualificationResponse response = new QueryOwnerBaseQualificationResponse();
        Boolean residenceResult = residencePermitService.hasResidencePermit(name, authId);
        response.setResidenceResult(residenceResult);
        return response;
    }


    /**
     * 查询外地驾照用户名下是否有新能源车
     * 针对外地驾照用户，查询其名下新能源车辆情况并更新审核状态
     */
    @Override
    public void queryOuterDriverHasNewEnergy() {
        // 查询外地需要核查的驾照记录
        List<OwnerTrafficQualification> trafficQualifications = driverService.queryOuterDriverPendingReview();
        if (trafficQualifications.isEmpty()) {
            return;
        }

        // 并行处理外地驾照审核
        processOuterDriverReviews(trafficQualifications);

        // 批量更新审核结果
        batchUpdateTrafficReviewResults(trafficQualifications);
    }

    /**
     * 并行处理外地驾照审核
     * 使用并行流提高处理效率，同时进行异常处理
     *
     * @param trafficQualifications 待审核的交通资格列表
     */
    private void processOuterDriverReviews(List<OwnerTrafficQualification> trafficQualifications) {
        trafficQualifications.parallelStream().forEach(data -> {
            try {
                // 获取对应的购车资格信息
                OwnerQualification qualification = getOwnerQualificationById(data.getOwnerQualificationId());

                // 查询名下新能源车情况（仅有驾照的用户）
                checkNewEnergyVehicleForDriver(data);

                // 执行交通自动审核
                data.setReviewStatus(trafficAutoApprove(qualification, data));

            } catch (Exception e) {
                // 记录异常但不中断处理流程
                log.error("处理外地驾照审核异常，资格ID: " + data.getOwnerQualificationId(), e);
            }
        });
    }

    /**
     * 检查驾照用户名下新能源车情况
     * 仅对有驾照的用户进行新能源车查询
     *
     * @param trafficQualification 交通资格信息
     */
    private void checkNewEnergyVehicleForDriver(OwnerTrafficQualification trafficQualification) {
        if (Integer.valueOf(1).equals(trafficQualification.getHasDriverLicense())) {
            Boolean hasNewEnergyVehicle = driverService.hasNewEnergyVehicle(trafficQualification.getDriverLicenseCode());
            trafficQualification.setHasNewEnergyVehicle(tranHasNewEnergyVehicle(hasNewEnergyVehicle));
        }
    }

    /**
     * 批量更新交通审核结果
     * 将审核完成的结果批量更新到数据库
     *
     * @param trafficQualifications 已审核的交通资格列表
     */
    private void batchUpdateTrafficReviewResults(List<OwnerTrafficQualification> trafficQualifications) {
        for (OwnerTrafficQualification item : trafficQualifications) {
            if (isReviewCompleted(item)) {
                // 更新交通资格表
                updateTrafficQualificationRecord(item);

                // 更新审核详情表
                updateReviewDetailRecord(item);
            } else {
                log.warn("本次公安核查 ownerQualificationId: {}, 未进行核查", item.getOwnerQualificationId());
            }
        }
    }

    /**
     * 检查审核是否已完成
     * 只有审核状态不为待审核（0）的记录才需要更新
     *
     * @param trafficQualification 交通资格信息
     * @return true表示审核已完成，false表示仍在待审核状态
     */
    private boolean isReviewCompleted(OwnerTrafficQualification trafficQualification) {
        return trafficQualification.getReviewStatus() != null
            && !Integer.valueOf(0).equals(trafficQualification.getReviewStatus());
    }

    /**
     * 更新交通资格记录
     * 更新新能源车拥有情况、审核状态和审核原因
     *
     * @param trafficQualification 交通资格信息
     */
    private void updateTrafficQualificationRecord(OwnerTrafficQualification trafficQualification) {
        UpdateStatementProvider updateTraffic = update(ownerTrafficQualification)
                .set(ownerTrafficQualification.hasNewEnergyVehicle).equalTo(trafficQualification.getHasNewEnergyVehicle())
                .set(ownerTrafficQualification.reviewStatus).equalTo(trafficQualification.getReviewStatus())
                .set(ownerTrafficQualification.reason).equalTo(trafficQualification.getReason())
                .where(ownerTrafficQualification.id, isEqualTo(trafficQualification.getId()))
                .and(ownerTrafficQualification.reviewStatus, isEqualTo(0))
                .build().render(RenderingStrategies.MYBATIS3);
        ownerQualificationReviewDetailMapper.update(updateTraffic);
    }

    /**
     * 更新审核详情记录
     * 更新购车资格审核详情表中的驾照审核状态和原因
     *
     * @param trafficQualification 交通资格信息
     */
    private void updateReviewDetailRecord(OwnerTrafficQualification trafficQualification) {
        UpdateStatementProvider updateReviewDetailStatement = update(ownerQualificationReviewDetail)
                .set(ownerQualificationReviewDetail.reviewStatus).equalTo(trafficQualification.getReviewStatus())
                .set(ownerQualificationReviewDetail.reason).equalTo(trafficQualification.getReason())
                .set(ownerQualificationReviewDetail.updatedUserId).equalTo(Const.ADMIN_ID)
                .set(ownerQualificationReviewDetail.updatedUserName).equalTo(Const.ADMIN_NAME)
                .set(ownerQualificationReviewDetail.updatedTime).equalTo(new Date())
                .where(ownerQualificationReviewDetail.reviewType, isEqualTo(REVIEW_TYPE_DRIVER))
                .and(ownerTrafficQualification.reviewStatus, isEqualTo(0))
                .and(ownerQualificationReviewDetail.ownerQulificationId, isEqualTo(trafficQualification.getOwnerQualificationId()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        ownerQualificationReviewDetailMapper.update(updateReviewDetailStatement);
    }

    /**
     * 办件办件编号查询相关联意向用户信息
     * @param applyNo 统一审批编码
     * @return OwnerQualification
     */
    private OwnerQualification queryOwnerQualificationByApplyNo(String applyNo) {
        if (StringUtils.isBlank(applyNo)) {
            return null;
        }
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where(ownerQualification.applyNo, isEqualTo(applyNo))
                .and(ownerQualification.qualificationType, isEqualTo(OwnerQualificationTypeEnum.NORMAL.getType()))
                .and(ownerQualification.status,isEqualTo(1))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        final Optional<OwnerQualification> result = ownerQualificationMapper.selectOne(selectStatement);
        return result.orElse(null);
    }

    /**
     * 受理用户资格相关申请的办件
     * 重构说明：将原来的复杂方法拆分为多个职责单一的私有方法，提高代码可读性和可维护性
     *
     * @param applyNo     统一审批编码
     * @param uapplyNo    联办审批编码
     * @param libraryInfo 办件信息
     */
    public void acceptQualificationApply(String applyNo, String uapplyNo, OwnerQualificationLibraryInfoDTO libraryInfo) {
        try {
            // 验证办件基础信息
            if (!validateLibraryInfo(applyNo, libraryInfo)) {
                return;
            }

            // 提取认证信息
            Integer authType = Integer.parseInt(libraryInfo.getAuthType().get("value"));
            String authId = libraryInfo.getAuthId();

            // 检查重复申请
            if (handleDuplicateApplication(applyNo, authType, authId)) {
                return;
            }

            // 保存申请信息并启动审核
            processQualificationApplication(applyNo, uapplyNo, libraryInfo, authType, authId);

        } catch (Exception e) {
            handleApplicationException(applyNo, uapplyNo, libraryInfo, e);
        }
    }

    /**
     * 验证办件基础信息
     * @param applyNo 申请编号
     * @param libraryInfo 办件信息
     * @return true表示验证通过，false表示验证失败
     */
    private boolean validateLibraryInfo(String applyNo, OwnerQualificationLibraryInfoDTO libraryInfo) {
        // 验证用户性质信息
        if (StringUtils.isBlank(libraryInfo.getProperty().get("value"))) {
            libraryService.refuseAcceptApply(applyNo, "您提交的办件信息存在异常，请确保填写信息正确后重新提交");
            return false;
        }

        // 验证证件信息
        Integer authType = Integer.parseInt(libraryInfo.getAuthType().get("value"));
        String authId = libraryInfo.getAuthId();
        if (authType == null || StringUtils.isBlank(authId)) {
            libraryService.refuseAcceptApply(applyNo, "您提交的证件信息存在异常，请确保填写信息正确后重新提交");
            return false;
        }

        // 验证驾驶证发证机关信息长度
        if (StringUtils.isNotBlank(libraryInfo.getDriverLicenseIssuingOrganization())
            && libraryInfo.getDriverLicenseIssuingOrganization().length() > 30) {
            libraryService.refuseAcceptApply(applyNo, "您提交的驾驶证发证机关填写信息异常，请确保填写信息正确后重新提交");
            return false;
        }

        // 验证车辆类型（混动车型限制）
        return validateVehicleType(applyNo, libraryInfo);
    }

    /**
     * 验证车辆类型，特别是混动车型的时间限制
     * @param applyNo 申请编号
     * @param libraryInfo 办件信息
     * @return true表示验证通过，false表示验证失败
     */
    private boolean validateVehicleType(String applyNo, OwnerQualificationLibraryInfoDTO libraryInfo) {
        if (libraryInfo.getVehicleModelType() != null) {
            // 检查混动车型的时间限制（2023年1月1日后不再接受）
            Date validDate = DateUtil.parse("2023-01-01", DateUtil.DATE_TYPE3);
            Integer vehicleType = Integer.valueOf(libraryInfo.getVehicleModelType().get("value"));

            if (new Date().after(validDate) && Integer.valueOf(2).equals(vehicleType)) {
                logger.warn("逾期提交混动购车资质申请: {}", applyNo);
                libraryService.refuseAcceptApply(applyNo,
                    "根据《上海市鼓励购买和使用新能源汽车实施办法》（沪府办规〔2021〕3号）相关要求，自2023年1月1日起，不再接受插电式混合动力车辆购车资质申请，请重新提交。");
                return false;
            }
        }
        return true;
    }

    /**
     * 处理重复申请检查
     * @param applyNo 申请编号
     * @param authType 认证类型
     * @param authId 认证ID
     * @return true表示存在重复申请需要终止，false表示可以继续处理
     */
    private boolean handleDuplicateApplication(String applyNo, Integer authType, String authId) {
        // 查询是否存在未完成的用户资格
        OwnerQualification unfinishedQualification = getUnfinishedQualification(authType, authId);
        if (unfinishedQualification != null) {
            return processDuplicateQualification(applyNo, unfinishedQualification);
        }
        return false;
    }

    /**
     * 处理重复的购车资格申请
     * @param applyNo 当前申请编号
     * @param unfinishedQualification 未完成的购车资格
     * @return true表示需要终止处理，false表示可以继续
     */
    private boolean processDuplicateQualification(String applyNo, OwnerQualification unfinishedQualification) {
        // 如果当前办件就是未完成的办件
        if (StringUtils.equals(unfinishedQualification.getApplyNo(), applyNo)) {
            return handleSameApplicationSync(applyNo, unfinishedQualification);
        } else {
            // 存在其他进行中的办件，不予受理
            handleOtherOngoingApplication(applyNo, unfinishedQualification);
            return true;
        }
    }

    /**
     * 处理相同申请的状态同步
     * @param applyNo 申请编号
     * @param qualification 购车资格信息
     * @return true表示需要终止处理，false表示可以继续
     */
    private boolean handleSameApplicationSync(String applyNo, OwnerQualification qualification) {
        Long oneHourAgo = System.currentTimeMillis() - TimeUnit.HOURS.toMillis(1);

        // 如果已经超过1个小时，同步办件状态
        if (qualification.getCreatedTime().getTime() < oneHourAgo) {
            libraryService.acceptApply(applyNo);

            // 根据数据库状态更新办件状态
            if (OwnerQualificationStatusEnum.APPROVE.getStatus().equals(qualification.getApplyStatus())) {
                libraryService.approveApply(applyNo);
            } else if (OwnerQualificationStatusEnum.DENY.getStatus().equals(qualification.getApplyStatus())) {
                libraryService.denyApply(applyNo, qualification.getReason());
            }
        }
        // 万达办件状态更新延迟，等待处理
        return true;
    }

    /**
     * 处理其他进行中的申请（不予受理）
     * @param applyNo 当前申请编号
     * @param unfinishedQualification 未完成的购车资格
     */
    private void handleOtherOngoingApplication(String applyNo, OwnerQualification unfinishedQualification) {
        String refuseReason = generateRefuseReason(applyNo, unfinishedQualification);
        libraryService.refuseAcceptApply(applyNo, refuseReason);
        libraryService.changeStatus(applyNo, AnnouncementEnum.QUALIFICATION_REFUSE_ACCEPT);
    }

    /**
     * 生成不予受理的原因说明
     * @param applyNo 当前申请编号
     * @param unfinishedQualification 未完成的购车资格
     * @return 不予受理原因
     */
    private String generateRefuseReason(String applyNo, OwnerQualification unfinishedQualification) {
        // 当存在未过期的审核通过办件时
        if (OwnerQualificationStatusEnum.APPROVE.getStatus().equals(unfinishedQualification.getApplyStatus())) {
            return String.format("由于您的购车资格查询已经通过，有效期至%s，故本次办件[%s]申请不予受理。请勿在有效期内重复查询",
                DateUtil.format(unfinishedQualification.getExpireTime(), DateUtil.DATE_TYPE3), applyNo);
        }
        // 当存在审核拒绝的办件时
        else {
            return generateDenyRefuseReason(applyNo, unfinishedQualification);
        }
    }

    /**
     * 生成拒绝状态的不予受理原因
     * @param applyNo 当前申请编号
     * @param unfinishedQualification 未完成的购车资格
     * @return 不予受理原因
     */
    private String generateDenyRefuseReason(String applyNo, OwnerQualification unfinishedQualification) {
        String applyTimeStr = DateUtil.format(unfinishedQualification.getApplyTime(), DateUtil.DATE_TYPE3);

        // 针对审批中的重复提交办件
        if (unfinishedQualification.getExpireTime() == null) {
            return String.format("由于您已于%s提交购车资质查询，目前正在审核中（不含提交当日和法定节假日，第5个工作日24点前出审核结果），故本次办件[%s]申请不予受理，请勿重复提交",
                applyTimeStr, applyNo);
        }
        // 针对复核中的重复提交办件
        else {
            String expireTimeStr = DateUtil.format(unfinishedQualification.getExpireTime(), DateUtil.DATE_TYPE3);
            return String.format("由于您已于%s提交购车资质查询，目前已进入补正流程，故本次办件[%s]申请不予受理，您可在%s前联系4S店提交补正材料或终止补正流程，补正流程可咨询021-58812035（工作日上午9:00-11:30，下午1:30-5:00）,期间请勿重复提交",
                applyTimeStr, applyNo, expireTimeStr);
        }
    }

    /**
     * 处理购车资格申请的主要业务逻辑
     * @param applyNo 申请编号
     * @param uapplyNo 联办申请编号
     * @param libraryInfo 办件信息
     * @param authType 认证类型
     * @param authId 认证ID
     */
    private void processQualificationApplication(String applyNo, String uapplyNo, OwnerQualificationLibraryInfoDTO libraryInfo, Integer authType, String authId) {
        // 创建购车资格信息
        OwnerQualification qualification = createOwnerQualification(libraryInfo, applyNo, uapplyNo);

        // 生成驾照信息（仅私人用户）
        DriverLicenseDTO driverLicense = generateDriverLicenseForPrivateUser(qualification, libraryInfo);

        // 保存购车资格信息
        saveOwnerQualificationInfo(qualification, "-1", "schedule");

        // 保存办件信息
        saveOnethingApplyInfo(applyNo, uapplyNo, qualification);

        // 受理办件并设置状态
        acceptApplicationAndSetStatus(applyNo);

        // 启动自动审核流程
        autoAssignQualificationReview(qualification, driverLicense);
    }

    /**
     * 创建购车资格信息对象
     * @param libraryInfo 办件信息
     * @param applyNo 申请编号
     * @param uapplyNo 联办申请编号
     * @return 购车资格信息对象
     */
    private OwnerQualification createOwnerQualification(OwnerQualificationLibraryInfoDTO libraryInfo, String applyNo, String uapplyNo) {
        OwnerQualification qualification = new OwnerQualification();

        // 设置基本信息
        qualification.setProperty(Integer.parseInt(libraryInfo.getProperty().get("value")));
        qualification.setName(StringUtils.isNotBlank(libraryInfo.getName()) ? libraryInfo.getName() : libraryInfo.getTargetName());
        qualification.setAuthId(libraryInfo.getAuthId());
        qualification.setAuthType(Integer.parseInt(libraryInfo.getAuthType().get("value")));
        qualification.setIsMilitaryOfficer(0);
        qualification.setMobilePhone(libraryInfo.getMobilePhone());
        qualification.setApplyNo(applyNo);
        qualification.setUapplyNo(uapplyNo);
        qualification.setUserId(libraryInfo.getUserId());

        // 设置申请经办人信息
        qualification.setApplyLicenseNo(libraryInfo.getLicenseNo());
        qualification.setApplyLicenseType(libraryInfo.getLicenseType());

        // 设置车辆类型
        if (libraryInfo.getVehicleModelType() != null) {
            qualification.setVehicleModelType(Integer.valueOf(libraryInfo.getVehicleModelType().get("value")));
        }

        return qualification;
    }

    /**
     * 为私人用户生成驾照信息
     * @param qualification 购车资格信息
     * @param libraryInfo 办件信息
     * @return 驾照信息DTO，如果不是私人用户则返回null
     */
    private DriverLicenseDTO generateDriverLicenseForPrivateUser(OwnerQualification qualification, OwnerQualificationLibraryInfoDTO libraryInfo) {
        if (qualification.getProperty() == 1) { // 私人用户
            // 设置户籍信息
            if (StringUtils.isNotBlank(libraryInfo.getHouseholdRegistrationType().get("value"))) {
                qualification.setHouseholdRegistrationType(Integer.parseInt(libraryInfo.getHouseholdRegistrationType().get("value")));
            }

            // 构建驾照信息
            return DriverLicenseDTO.builder()
                    .driverLicenseCode(libraryInfo.getDriverLicenseCode())
                    .driverLicenseIssuingPlace(libraryInfo.getDriverLicenseIssuingPlace().get("name"))
                    .driverLicenseIssuingOrganization(libraryInfo.getDriverLicenseIssuingOrganization())
                    .driverFileNo(libraryInfo.getDriverFileNo())
                    .build();
        }
        return null;
    }

    /**
     * 保存办件信息
     * @param applyNo 申请编号
     * @param uapplyNo 联办申请编号
     * @param qualification 购车资格信息
     */
    private void saveOnethingApplyInfo(String applyNo, String uapplyNo, OwnerQualification qualification) {
        OnethingApplyInfo applyInfo = new OnethingApplyInfo();
        applyInfo.setItemCode(LibraryApplyItemEnum.OWNER_QUALIFICATION_APPLY.getItemCode());
        applyInfo.setApplyNo(applyNo);
        applyInfo.setUapplyNo(uapplyNo);
        applyInfo.setOwnerQualificationId(qualification.getId());
        applyInfo.setApplyStatus(ApplyStatusEnum.WAITING_ACCEPT.getStatus());
        applyInfo.setCreatedTime(new Date());
        applyInfo.setCreatedUserId(qualification.getCreatedUserId());
        applyInfo.setCreatedUserName(qualification.getCreatedUserName());
        onethingApplyInfoMapper.insertSelective(applyInfo);
    }

    /**
     * 受理申请并设置状态
     * @param applyNo 申请编号
     */
    private void acceptApplicationAndSetStatus(String applyNo) {
        // 受理办件
        libraryService.acceptApply(applyNo);
        // 设置指南页面（已废弃，保留兼容性）
        libraryService.changeStatus(applyNo, AnnouncementEnum.QUALIFICATION_ACCEPTED);
    }

    /**
     * 处理申请异常情况
     * @param applyNo 申请编号
     * @param uapplyNo 联办申请编号
     * @param libraryInfo 办件信息
     * @param e 异常信息
     */
    private void handleApplicationException(String applyNo, String uapplyNo, OwnerQualificationLibraryInfoDTO libraryInfo, Exception e) {
        logger.error("办件参数异常: {}, 办件编号: {}, 联办件编号: {}, 办件信息: {}",
            e.getMessage(), applyNo, uapplyNo, libraryInfo, e);
        // 设置指南页面为不予受理状态
        libraryService.changeStatus(applyNo, AnnouncementEnum.QUALIFICATION_REFUSE_ACCEPT);
    }

    /**
     * 自动审批用户资格信息（全量）
     * 针对所有目前购车资格状态为核查中的用户，自动进行核查办件
     */
    @Override
    public BaseResponse autoReviewOwnerQualification() {
        //查询所有核查中的购车资格信息
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where(ownerQualification.applyStatus, isEqualTo(0))
                .and(ownerQualification.status,isEqualTo(1))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        final List<OwnerQualification> ownerQualificationList = ownerQualificationMapper.selectMany(selectStatement);
        //遍历用户查询，自动审核用户购车资质信息
        for(OwnerQualification needReviewOwnerQualification: ownerQualificationList){
            asyncService.autoReviewOwnerQualification(needReviewOwnerQualification);
        }
        return new BaseResponse();
    }

    /**
     * 根据办件信息，生成驾照信息
     * @param property 用户性质
     * @param libraryInfo 办件信息
     * @return
     */
    private DriverLicenseDTO generateDriverLicenseInfo(Integer property, OwnerQualificationLibraryInfoDTO libraryInfo){
        DriverLicenseDTO driverLicense = null;
        if (property == 1) {
            driverLicense = DriverLicenseDTO.builder()
                    .driverLicenseCode(libraryInfo.getDriverLicenseCode())
                    .driverLicenseIssuingPlace(libraryInfo.getDriverLicenseIssuingPlace().get("name"))
                    .driverLicenseIssuingOrganization(libraryInfo.getDriverLicenseIssuingOrganization())
                    .driverFileNo(libraryInfo.getDriverFileNo())
                    .build();
        }
        return driverLicense;
    }

    /**
     * 修复用户购车资质信息
     * @param reviewOwnerQualificationInfo 需修复的购车资质申请信息
     */
    private void recoverOwnerQualificationReviewDetail(OwnerQualification reviewOwnerQualificationInfo){
        //由于小程序的办件库信息不全，无法复原，所以直接拒绝，要求用户重新提交或者不提交
        if(StringUtils.compare(reviewOwnerQualificationInfo.getCreatedUserName(), Const.SMALL_PROGRAM)==0){
            libraryService.refuseAcceptApply(reviewOwnerQualificationInfo.getApplyNo(), "办件处理异常，请重新提交");
            return;
        }

        //重新从办件库中获取信息
        GetApplyBaseResponse applyBaseResponse = libraryService.getApplyBase(reviewOwnerQualificationInfo.getApplyNo());
        if (applyBaseResponse == null || applyBaseResponse.getData() == null || applyBaseResponse.getData().size() == 0) {
            return;
        }
        final GetApplyBaseDTO applyBaseInfo = applyBaseResponse.getData().get(0);
        try {
            OwnerQualificationLibraryInfoDTO libraryInfo = new OwnerQualificationLibraryInfoDTO();
            //将办件库的Map数据转化成指定类进行处理
            BeanUtils.populate(libraryInfo, applyBaseInfo.getInfo());

            //针对私人用户，填充户籍信息
            if (reviewOwnerQualificationInfo.getProperty() == 1 && StringUtils.isNotBlank(libraryInfo.getHouseholdRegistrationType().get("value"))){
                reviewOwnerQualificationInfo.setHouseholdRegistrationType(Integer.parseInt(libraryInfo.getHouseholdRegistrationType().get("value")));
            }
            //组装驾照信息
            DriverLicenseDTO driverLicense = generateDriverLicenseInfo(reviewOwnerQualificationInfo.getProperty(), libraryInfo);


            //判断是否是特殊人才
            boolean isSpecialOwner = ownerQualificationSpecialService.isSpecialOwner(reviewOwnerQualificationInfo.getAuthId());
            if(isSpecialOwner){
                logger.warn("该用户：{}为特殊人才，略过常规校验。", reviewOwnerQualificationInfo.getAuthId());
                //查询驾照信息-仅查询名下是否有新能源车
                if (reviewOwnerQualificationInfo.getProperty() != 2) {
                    driverService.review(reviewOwnerQualificationInfo.getId(), driverLicense, true);
                }
            }

            //正常流程
            else{
                //查询信用信息
                creditService.review(reviewOwnerQualificationInfo.getId(), reviewOwnerQualificationInfo.getProperty(), reviewOwnerQualificationInfo.getName(), reviewOwnerQualificationInfo.getAuthId());

                //查询驾照信息（除法人）
                if (reviewOwnerQualificationInfo.getProperty() != 2) {
                    driverService.review(reviewOwnerQualificationInfo.getId(), driverLicense);
                }

                if (reviewOwnerQualificationInfo.getProperty() == 2 || reviewOwnerQualificationInfo.getHouseholdRegistrationType() != 0) {
                    newSocialService.review(reviewOwnerQualificationInfo.getId(), reviewOwnerQualificationInfo.getName(), reviewOwnerQualificationInfo.getAuthType(), reviewOwnerQualificationInfo.getAuthId());
                }

                //查询居住证（外省市（不包括港澳台））除军官
                if (reviewOwnerQualificationInfo.getProperty() != 2 && reviewOwnerQualificationInfo.getHouseholdRegistrationType() == 1 && reviewOwnerQualificationInfo.getIsMilitaryOfficer() == 0) {
                    residencePermitService.review(reviewOwnerQualificationInfo.getId(),reviewOwnerQualificationInfo.getName(), reviewOwnerQualificationInfo.getAuthId());
                }

                //交通委大牌额度
                if(reviewOwnerQualificationInfo.getProperty() != 2){
                    trafficCommitteeService.reviewLicenseLimit(reviewOwnerQualificationInfo.getId());
                }
            }


            //针对为待预审的办件，进行受理
            if (StringUtils.compare(applyBaseInfo.getStatus(), "待预审") == 0 || StringUtils.compare(applyBaseInfo.getStatus(), "不予受理") == 0) {
                //受理办件
                libraryService.acceptApply(reviewOwnerQualificationInfo.getApplyNo());
                //设置指南页面
                libraryService.changeStatus(reviewOwnerQualificationInfo.getApplyNo(), AnnouncementEnum.QUALIFICATION_ACCEPTED);
            }
        } catch (Exception e) {
            //终止办件
            logger.error(e.getMessage(),e);
        }
    }


    /**
     * 自动审批用户资格信息
     * 重构说明：将原来的复杂方法拆分为多个职责单一的私有方法，提高代码可读性和可维护性
     *
     * @param reviewOwnerQualificationInfo 待审核的购车资格信息
     */
    @Override
    public void autoReviewOwnerQualification(OwnerQualification reviewOwnerQualificationInfo) {
        // 查询审核详情列表
        List<OwnerQualificationReviewDetail> reviewDetailList = queryOwnerQualificationReviewDetailList(reviewOwnerQualificationInfo.getId());

        // 处理异常办件（审核详情为空的情况）
        if (reviewDetailList.isEmpty()) {
            handleAbnormalQualification(reviewOwnerQualificationInfo);
            return;
        }

        // 处理特殊类型的资格审核
        if (handleSpecialQualificationTypes(reviewOwnerQualificationInfo, reviewDetailList)) {
            return;
        }

        // 处理普通购车资格审核
        handleNormalQualificationReview(reviewOwnerQualificationInfo, reviewDetailList);
    }

    /**
     * 查询购车资格审核详情列表
     * @param qualificationId 购车资格ID
     * @return 审核详情列表
     */
    private List<OwnerQualificationReviewDetail> queryOwnerQualificationReviewDetailList(Long qualificationId) {
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerQualificationReviewDetail.allColumns())
                .from(ownerQualificationReviewDetail)
                .where(ownerQualificationReviewDetail.ownerQulificationId, isEqualTo(qualificationId))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return ownerQualificationReviewDetailMapper.selectMany(selectStatement);
    }

    /**
     * 处理异常办件（审核详情为空的情况）
     * @param qualification 购车资格信息
     */
    private void handleAbnormalQualification(OwnerQualification qualification) {
        logger.error("办件处理异常，办件信息：{}-{}", qualification.getId(), qualification.getApplyNo());

        if (StringUtils.isNotBlank(qualification.getApplyNo())) {
            // 有办件编号，尝试修复
            recoverOwnerQualificationReviewDetail(qualification);
        } else {
            // 无办件编号，直接拒绝
            updateQualificationStatus(
                qualification.getId(),
                OwnerQualificationStatusEnum.DENY.getStatus(),
                "提交信息异常，请重新提交申请",
                null,
                "-1",
                "system"
            );
        }
    }

    /**
     * 处理特殊类型的资格审核（以旧换新、军官证）
     * @param qualification 购车资格信息
     * @param reviewDetailList 审核详情列表
     * @return true表示已处理，false表示需要继续普通流程
     */
    private boolean handleSpecialQualificationTypes(OwnerQualification qualification, List<OwnerQualificationReviewDetail> reviewDetailList) {
        // 以旧换新资格审核
        if (Objects.equals(qualification.getQualificationType(), OwnerQualificationTypeEnum.EXCHANGE.getType())) {
            autoReviewExchangeQualification(qualification, reviewDetailList);
            return true;
        }

        // 军官证资格审核
        if (Integer.valueOf(1).equals(qualification.getIsMilitaryOfficer())) {
            autoReviewMilitaryQualification(qualification, reviewDetailList);
            return true;
        }

        return false;
    }

    /**
     * 处理普通购车资格审核的主流程
     * @param qualification 购车资格信息
     * @param reviewDetailList 审核详情列表
     */
    private void handleNormalQualificationReview(OwnerQualification qualification, List<OwnerQualificationReviewDetail> reviewDetailList) {
        // 分析审核结果
        ReviewAnalysisResult analysisResult = analyzeReviewResults(reviewDetailList);

        // 处理测试环境特殊逻辑
        handleTestEnvironment(qualification, analysisResult);

        // 如果审核完成，处理最终结果
        if (analysisResult.isFinishReview()) {
            if (analysisResult.isDeny()) {
                processDenyResult(qualification, reviewDetailList, analysisResult.getReason());
            } else {
                processApprovalResult(qualification);
            }
        }
    }

    /**
     * 分析审核结果，汇总各部门审核状态
     * @param reviewDetailList 审核详情列表
     * @return 审核分析结果
     */
    private ReviewAnalysisResult analyzeReviewResults(List<OwnerQualificationReviewDetail> reviewDetailList) {
        boolean finishReview = true;
        boolean isDeny = false;
        StringBuilder reason = new StringBuilder();

        for (OwnerQualificationReviewDetail reviewDetail : reviewDetailList) {
            switch (reviewDetail.getReviewStatus()) {
                case 0: // 待核查
                    finishReview = false;
                    break;
                case 1: // 通过
                    break;
                case 2: // 拒绝
                case 3: // 未查询相关信息
                    isDeny = true;
                    if (reviewDetail.getReason() != null) {
                        reason.append(reviewDetail.getReason()).append(";");
                    }
                    break;
                default:
                    break;
            }
        }

        return new ReviewAnalysisResult(finishReview, isDeny, reason.toString());
    }

    /**
     * 处理测试环境的特殊逻辑
     * @param qualification 购车资格信息
     * @param analysisResult 审核分析结果
     */
    private void handleTestEnvironment(OwnerQualification qualification, ReviewAnalysisResult analysisResult) {
        if ("test".equals(SpringContextUtil.getActiveProfile()) && qualification.getProperty() == 1) {
            // 测试环境下私人用户默认走复核流程
            analysisResult.setFinishReview(true);
            analysisResult.setDeny(true);
        }
    }

    /**
     * 处理审核通过的情况
     * @param qualification 购车资格信息
     */
    private void processApprovalResult(OwnerQualification qualification) {
        // 更新办件库状态为通过
        libraryService.approveApply(qualification.getApplyNo());

        // 设置指南页面
        libraryService.changeStatus(qualification.getApplyNo(), AnnouncementEnum.QUALIFICATION_APPROVE);

        // 发送审核通过短信
        sendApprovalMessage(qualification.getMobilePhone());

        // 更新资格状态
        Date expireTime = calculateExpirationTime();
        updateQualificationStatus(
            qualification.getId(),
            OwnerQualificationStatusEnum.APPROVE.getStatus(),
            "",
            expireTime,
            "-1",
            "system"
        );
    }

    /**
     * 处理审核拒绝的情况
     * @param qualification 购车资格信息
     * @param reviewDetailList 审核详情列表
     * @param reason 拒绝原因
     */
    private void processDenyResult(OwnerQualification qualification, List<OwnerQualificationReviewDetail> reviewDetailList, String reason) {
        // 判断能否复核
        boolean canReconsider = determineCanReconsider(qualification, reviewDetailList);

        if (canReconsider) {
            processReconsiderationResult(qualification, reason);
        } else {
            processFinalDenyResult(qualification, reason);
        }
    }

    /**
     * 判断是否可以复核
     * @param qualification 购车资格信息
     * @param reviewDetailList 审核详情列表
     * @return 是否可以复核
     */
    private boolean determineCanReconsider(OwnerQualification qualification, List<OwnerQualificationReviewDetail> reviewDetailList) {
        // 判断能否复核（只有一项被拒绝，且该项为信用未记录）
        boolean canReconsider = canQualificationReconsider(qualification, reviewDetailList);

        // 异常数据处理
        if (reviewDetailList.isEmpty()) {
            canReconsider = false;
        }

        // 测试环境特殊处理
        if ("test".equals(SpringContextUtil.getActiveProfile()) && qualification.getProperty() == 1) {
            canReconsider = true;
        }

        return canReconsider;
    }

    /**
     * 处理复核情况
     * @param qualification 购车资格信息
     * @param reason 拒绝原因
     */
    private void processReconsiderationResult(OwnerQualification qualification, String reason) {
        // 查询是否已经上传扫描件
        List<FileInfoDTO> fileInfo = fileService.getFileInfo(FileTypeEnum.QUALIFICATION_PROOF_COPY, qualification.getId());

        if (fileInfo.size() > 0) {
            // 已上传复核扫描件，直接进入复核状态
            updateQualificationStatus(
                qualification.getId(),
                OwnerQualificationStatusEnum.RECONSIDERATION.getStatus(),
                reason,
                null,
                "-1",
                "system"
            );
        } else {
            // 未上传扫描件，发送补正通知
            processSupplementNotification(qualification, reason);
        }
    }

    /**
     * 处理补正通知
     * @param qualification 购车资格信息
     * @param reason 拒绝原因
     */
    private void processSupplementNotification(OwnerQualification qualification, String reason) {
        // 发送补正材料短信
        sendSupplementMessage(qualification.getMobilePhone(), reason);

        // 更新办件状态为补正状态
        libraryService.supplementApply(qualification.getApplyNo(),
            String.format(SUPPLEMENT_MESSAGE_TEMPLATE, reason));

        // 设置指南页面
        libraryService.changeStatus(qualification.getApplyNo(), AnnouncementEnum.QUALIFICATION_SUPPLEMENT);

        // 更新资格状态（设置30天有效期）
        Date expireTime = calculateReconsiderationExpirationTime();
        updateQualificationStatus(
            qualification.getId(),
            OwnerQualificationStatusEnum.DENY.getStatus(),
            reason,
            expireTime,
            "-1",
            "system"
        );
    }

    /**
     * 处理最终拒绝结果
     * @param qualification 购车资格信息
     * @param reason 拒绝原因
     */
    private void processFinalDenyResult(OwnerQualification qualification, String reason) {
        // 发送拒绝短信
        sendDenyMessage(qualification.getMobilePhone(), reason);

        // 更新办件状态为拒绝
        libraryService.denyApply(qualification.getApplyNo(),
            String.format(DENY_MESSAGE_TEMPLATE, reason));

        // 设置指南页面
        libraryService.changeStatus(qualification.getApplyNo(), AnnouncementEnum.QUALIFICATION_DENY);

        // 更新资格状态
        updateQualificationStatus(
            qualification.getId(),
            OwnerQualificationStatusEnum.DENY.getStatus(),
            reason,
            null,
            "-1",
            "system"
        );
    }

    /**
     * 自动审核以旧换新资质
     * 以旧换新资质有特殊的复核规则，需要区分军官和普通用户
     *
     * @param qualification 以旧换新资质基本信息
     * @param reviewDetailList 以旧换新各部门审批明细
     */
    private void autoReviewExchangeQualification(OwnerQualification qualification, List<OwnerQualificationReviewDetail> reviewDetailList){
        // 处理异常办件
        if (reviewDetailList == null || reviewDetailList.isEmpty()) {
            log.error("以旧换新资质信息异常:" + JSON.toJSONString(qualification));
            return;
        }

        // 检查是否所有审核都已完成
        if (!isAllReviewsCompleted(reviewDetailList)) {
            return; // 未完成全部核查，不做任何处理
        }

        // 分析审核结果
        ExchangeReviewResult reviewResult = analyzeExchangeReviewResults(reviewDetailList);

        // 确定最终申请状态
        Integer finalStatus = determineExchangeFinalStatus(qualification, reviewDetailList, reviewResult);

        // 计算有效期
        Date expireTime = calculateExchangeExpireTime(reviewResult.isCanReconsider());

        // 更新资格状态
        updateExchangeQualificationStatus(qualification, finalStatus, reviewResult.getReason(), expireTime);
    }

    /**
     * 检查是否所有审核都已完成
     */
    private boolean isAllReviewsCompleted(List<OwnerQualificationReviewDetail> reviewDetailList) {
        return reviewDetailList.stream()
                .noneMatch(reviewDetail -> Objects.equals(reviewDetail.getReviewStatus(), OwnerQualificationReviewStatusEnum.PENDING.getStatus()));
    }

    /**
     * 分析以旧换新审核结果
     */
    private ExchangeReviewResult analyzeExchangeReviewResults(List<OwnerQualificationReviewDetail> reviewDetailList) {
        boolean isDeny = false;
        StringBuilder reason = new StringBuilder();

        for (OwnerQualificationReviewDetail reviewDetail : reviewDetailList) {
            if (reviewDetail.getReviewStatus() == REVIEW_STATUS_DENY || reviewDetail.getReviewStatus() == REVIEW_STATUS_NOT_FOUND) {
                isDeny = true;
                if (reviewDetail.getReason() != null) {
                    reason.append(reviewDetail.getReason()).append(";");
                }
            }
        }

        return new ExchangeReviewResult(isDeny, reason.toString());
    }

    /**
     * 确定以旧换新的最终申请状态
     */
    private Integer determineExchangeFinalStatus(OwnerQualification qualification, List<OwnerQualificationReviewDetail> reviewDetailList, ExchangeReviewResult reviewResult) {
        if (!reviewResult.isDeny()) {
            return OwnerQualificationStatusEnum.APPROVE.getStatus();
        }

        // 判断能否复核
        boolean canReconsider = determineExchangeReconsiderationEligibility(qualification, reviewDetailList);
        reviewResult.setCanReconsider(canReconsider);

        if (canReconsider) {
            return OwnerQualificationStatusEnum.RECONSIDERATION.getStatus();
        } else {
            return OwnerQualificationStatusEnum.DENY.getStatus();
        }
    }

    /**
     * 判断以旧换新是否可以复核
     */
    private boolean determineExchangeReconsiderationEligibility(OwnerQualification qualification, List<OwnerQualificationReviewDetail> reviewDetailList) {
        if (Objects.equals(qualification.getIsMilitaryOfficer(), IS_MILITARY_OFFICER)) {
            // 军官复核规则
            return checkMilitaryExchangeReconsiderationEligibility(reviewDetailList);
        } else {
            // 普通用户复核规则
            return checkRegularExchangeReconsiderationEligibility(reviewDetailList);
        }
    }

    /**
     * 检查军官以旧换新复核资格
     */
    private boolean checkMilitaryExchangeReconsiderationEligibility(List<OwnerQualificationReviewDetail> reviewDetailList) {
        for (OwnerQualificationReviewDetail reviewDetail : reviewDetailList) {
            if (reviewDetail.getReviewStatus() == REVIEW_STATUS_DENY) {
                // 军官不校验社保、居住证、交委
                if (!Objects.equals(reviewDetail.getReviewType(), OwnerQualificationReviewTypeEnum.RESIDENCE.getType())
                    && !Objects.equals(reviewDetail.getReviewType(), OwnerQualificationReviewTypeEnum.SOCIAL.getType())
                    && !Objects.equals(reviewDetail.getReviewType(), OwnerQualificationReviewTypeEnum.TRAFFIC.getType())) {
                    return false;
                }
            } else if (reviewDetail.getReviewStatus() == REVIEW_STATUS_NOT_FOUND) {
                // 未查询相关信息（只校验公安）
                if (Objects.equals(reviewDetail.getReviewType(), OwnerQualificationReviewTypeEnum.DRIVER.getType())) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 检查普通用户以旧换新复核资格
     */
    private boolean checkRegularExchangeReconsiderationEligibility(List<OwnerQualificationReviewDetail> reviewDetailList) {
        // 信用、驾照、居住证被审核拒绝后，直接无法复议
        return reviewDetailList.stream()
                .filter(reviewDetail -> Objects.equals(OwnerQualificationReviewStatusEnum.DENY.getStatus(), reviewDetail.getReviewStatus())
                        || Objects.equals(OwnerQualificationReviewStatusEnum.NOT_FOUND.getStatus(), reviewDetail.getReviewStatus()))
                .noneMatch(reviewDetail -> Objects.equals(OwnerQualificationReviewTypeEnum.DRIVER.getType(), reviewDetail.getReviewType())
                        || Objects.equals(OwnerQualificationReviewTypeEnum.RESIDENCE.getType(), reviewDetail.getReviewType())
                        || Objects.equals(OwnerQualificationReviewTypeEnum.CREDIT.getType(), reviewDetail.getReviewType()));
    }

    /**
     * 计算以旧换新有效期
     */
    private Date calculateExchangeExpireTime(boolean canReconsider) {
        if (!canReconsider) {
            return null;
        }

        Date now = new Date();
        // 如果是2025年之前提交的，则有效期至2024-12-31
        if (now.before(DateUtil.parse("2025-01-01 00:00:00", DateUtil.DATE_TYPE1))) {
            return DateUtil.parse("2024-12-31", DateUtil.DATE_TYPE3);
        } else {
            // 有效期为90天
            return new Date(TimeUnit.DAYS.toMillis(QUALIFICATION_VALID_DAYS) + now.getTime());
        }
    }

    /**
     * 更新以旧换新资格状态
     */
    private void updateExchangeQualificationStatus(OwnerQualification qualification, Integer status, String reason, Date expireTime) {
        Date now = new Date();
        UpdateStatementProvider updateStatement = update(ownerQualification)
                .set(ownerQualification.applyStatus).equalTo(status)
                .set(ownerQualification.reason).equalTo(reason)
                .set(ownerQualification.expireTime).equalToWhenPresent(expireTime)
                .set(ownerQualification.reviewTime).equalTo(now)
                .set(ownerQualification.updatedTime).equalTo(now)
                .set(ownerQualification.updatedUserId).equalTo("-1")
                .set(ownerQualification.updatedUserName).equalTo("system")
                .where(ownerQualification.id, isEqualTo(qualification.getId()))
                .and(ownerQualification.applyStatus, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        ownerQualificationMapper.update(updateStatement);
    }

    /**
     * 以旧换新审核结果内部类
     */
    private static class ExchangeReviewResult {
        private final boolean deny;
        private final String reason;
        private boolean canReconsider;

        public ExchangeReviewResult(boolean deny, String reason) {
            this.deny = deny;
            this.reason = reason;
        }

        public boolean isDeny() { return deny; }
        public String getReason() { return reason; }
        public boolean isCanReconsider() { return canReconsider; }
        public void setCanReconsider(boolean canReconsider) { this.canReconsider = canReconsider; }
    }



    /**
     * 自动审核军官证资质
     * 军官证审核有特殊规则：不校验社保，只有公安信息未查询到才拒绝
     *
     * @param qualification 购车资格信息
     * @param reviewDetailList 用户资格各部门核查结果
     */
    private void autoReviewMilitaryQualification(OwnerQualification qualification, List<OwnerQualificationReviewDetail> reviewDetailList) {
        // 分析军官证审核结果
        MilitaryReviewResult reviewResult = analyzeMilitaryReviewResults(reviewDetailList);

        // 如果审核未完成，不做任何处理
        if (!reviewResult.isFinishReview()) {
            return;
        }

        // 处理审核完成的情况
        if (reviewResult.isDeny()) {
            processMilitaryDenyResult(qualification, reviewResult.getReason());
        }

        // 更新军官证资格状态
        updateMilitaryQualificationStatus(qualification, reviewResult);
    }

    /**
     * 分析军官证审核结果
     */
    private MilitaryReviewResult analyzeMilitaryReviewResults(List<OwnerQualificationReviewDetail> reviewDetailList) {
        boolean finishReview = true;
        boolean isDeny = false;
        StringBuilder reason = new StringBuilder();

        for (OwnerQualificationReviewDetail reviewDetail : reviewDetailList) {
            switch (reviewDetail.getReviewStatus()) {
                case REVIEW_STATUS_PENDING:
                    // 待核查
                    finishReview = false;
                    break;
                case REVIEW_STATUS_APPROVE:
                    // 通过，无需处理
                    break;
                case REVIEW_STATUS_DENY:
                    // 拒绝（不校验社保）
                    if (reviewDetail.getReviewType() != REVIEW_TYPE_SOCIAL) {
                        isDeny = true;
                    }
                    if (reviewDetail.getReason() != null) {
                        reason.append(reviewDetail.getReason()).append(";");
                    }
                    break;
                case REVIEW_STATUS_NOT_FOUND:
                    // 未查询相关信息（只校验公安）
                    if (reviewDetail.getReviewType() == REVIEW_TYPE_DRIVER) {
                        isDeny = true;
                    }
                    if (reviewDetail.getReason() != null) {
                        reason.append(reviewDetail.getReason()).append(";");
                    }
                    break;
                default:
                    break;
            }
        }

        return new MilitaryReviewResult(finishReview, isDeny, reason.toString());
    }

    /**
     * 处理军官证拒绝结果
     */
    private void processMilitaryDenyResult(OwnerQualification qualification, String reason) {
        // 更新办件状态为拒绝
        libraryService.denyApply(qualification.getApplyNo(), reason);

        // 设置指南页面
        libraryService.changeStatus(qualification.getApplyNo(), AnnouncementEnum.QUALIFICATION_DENY);

        // 发送拒绝短信通知
        sendDenyMessage(qualification.getMobilePhone(), reason);
    }

    /**
     * 更新军官证资格状态
     */
    private void updateMilitaryQualificationStatus(OwnerQualification qualification, MilitaryReviewResult reviewResult) {
        Date now = new Date();
        Integer finalStatus = reviewResult.isDeny() ?
            OwnerQualificationStatusEnum.DENY.getStatus() :
            OwnerQualificationStatusEnum.RECONSIDERATION.getStatus();

        UpdateStatementProvider updateStatement = update(ownerQualification)
                .set(ownerQualification.applyStatus).equalTo(finalStatus)
                .set(ownerQualification.reason).equalTo(reviewResult.getReason())
                .set(ownerQualification.reviewTime).equalTo(now)
                .set(ownerQualification.updatedTime).equalTo(now)
                .set(ownerQualification.updatedUserId).equalTo(Const.ADMIN_ID)
                .set(ownerQualification.updatedUserName).equalTo(Const.ADMIN_NAME)
                .where(ownerQualification.id, isEqualTo(qualification.getId()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        ownerQualificationMapper.update(updateStatement);
    }

    /**
     * 军官证审核结果内部类
     */
    private static class MilitaryReviewResult {
        private final boolean finishReview;
        private final boolean deny;
        private final String reason;

        public MilitaryReviewResult(boolean finishReview, boolean deny, String reason) {
            this.finishReview = finishReview;
            this.deny = deny;
            this.reason = reason;
        }

        public boolean isFinishReview() { return finishReview; }
        public boolean isDeny() { return deny; }
        public String getReason() { return reason; }
    }


    /**
     * 判断购车资格是否可以复核
     * 查询审核详情并调用重载方法进行复核条件判断
     *
     * @param reviewOwnerQualificationInfo 购车资格信息
     * @return true表示可以复核，false表示不可复核
     */
    private Boolean canQualificationReconsider(OwnerQualification reviewOwnerQualificationInfo) {
        // 查询该购车资格的所有审核详情
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerQualificationReviewDetail.allColumns())
                .from(ownerQualificationReviewDetail)
                .where(ownerQualificationReviewDetail.ownerQulificationId, isEqualTo(reviewOwnerQualificationInfo.getId()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        List<OwnerQualificationReviewDetail> ownerQualificationReviewDetailList = ownerQualificationReviewDetailMapper.selectMany(selectStatement);

        // 调用重载方法进行具体的复核条件判断
        return canQualificationReconsider(reviewOwnerQualificationInfo, ownerQualificationReviewDetailList);
    }


    /**
     * 判断购车资格是否可以复核（重载方法）
     * 根据用户类型和审核详情判断是否符合复核条件
     *
     * @param reviewOwnerQualificationInfo 购车资格信息
     * @param ownerQualificationReviewDetailList 审核详情列表
     * @return true表示可以复核，false表示不可复核
     */
    public static Boolean canQualificationReconsider(OwnerQualification reviewOwnerQualificationInfo, List<OwnerQualificationReviewDetail> ownerQualificationReviewDetailList) {
        // 基础条件检查
        if (!isBasicReconsiderationEligible(reviewOwnerQualificationInfo, ownerQualificationReviewDetailList)) {
            return false;
        }

        // 根据用户类型进行不同的复核条件判断
        if (reviewOwnerQualificationInfo.getProperty() == 1) {
            // 私人用户复核条件判断
            return checkPrivateUserReconsiderationEligibility(reviewOwnerQualificationInfo, ownerQualificationReviewDetailList);
        } else {
            // 企业用户复核条件判断
            return checkBusinessUserReconsiderationEligibility(ownerQualificationReviewDetailList);
        }
    }

    /**
     * 检查基础复核资格
     * @param qualification 购车资格信息
     * @param reviewDetailList 审核详情列表
     * @return 是否符合基础复核条件
     */
    private static boolean isBasicReconsiderationEligible(OwnerQualification qualification, List<OwnerQualificationReviewDetail> reviewDetailList) {
        // 只有私人用户才能复核
        if (qualification.getProperty() != 1) {
            return true; // 企业用户走不同的逻辑
        }

        // 申请时间必须在2021年3月1日之后
        if (qualification.getApplyTime().getTime() < DateUtil.parse("20210301", DateUtil.DATE_TYPE4).getTime()) {
            return false;
        }

        // 由于办件库异常导致数据都没有的不能复核
        return !reviewDetailList.isEmpty();
    }

    /**
     * 检查私人用户复核资格
     * @param qualification 购车资格信息
     * @param reviewDetailList 审核详情列表
     * @return 是否符合复核条件
     */
    private static boolean checkPrivateUserReconsiderationEligibility(OwnerQualification qualification, List<OwnerQualificationReviewDetail> reviewDetailList) {
        for (OwnerQualificationReviewDetail reviewDetail : reviewDetailList) {
            // 检查信用审核条件
            if (!checkCreditReviewForReconsideration(reviewDetail, qualification.getAuthType())) {
                return false;
            }

            // 检查驾照审核条件
            if (!checkDriverLicenseReviewForReconsideration(reviewDetail)) {
                return false;
            }

            // 检查居住证审核条件
            if (!checkResidencePermitReviewForReconsideration(reviewDetail)) {
                return false;
            }

            // 检查交委审核条件
            if (!checkTrafficCommitteeReviewForReconsideration(reviewDetail)) {
                return false;
            }

            // 检查社保审核条件
            if (!checkSocialInsuranceReviewForReconsideration(reviewDetail)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查企业用户复核资格
     * @param reviewDetailList 审核详情列表
     * @return 是否符合复核条件
     */
    private static boolean checkBusinessUserReconsiderationEligibility(List<OwnerQualificationReviewDetail> reviewDetailList) {
        for (OwnerQualificationReviewDetail reviewDetail : reviewDetailList) {
            // 信用不为【审核通过】则不能复核
            if (OwnerQualificationReviewTypeEnum.CREDIT.getType().equals(reviewDetail.getReviewType())
                    && !OwnerQualificationReviewStatusEnum.APPROVE.getStatus().equals(reviewDetail.getReviewStatus())) {
                return false;
            }

            // 社保不为【审核不通过】，则不能复核
            if (OwnerQualificationReviewTypeEnum.SOCIAL.getType().equals(reviewDetail.getReviewType())
                    && !OwnerQualificationReviewStatusEnum.DENY.getStatus().equals(reviewDetail.getReviewStatus())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查信用审核是否符合复核条件
     */
    private static boolean checkCreditReviewForReconsideration(OwnerQualificationReviewDetail reviewDetail, Integer authType) {
        if (reviewDetail.getReviewType() == REVIEW_TYPE_CREDIT) { // 信用审核
            // 信用【未完成】或【不通过】不能复核
            if (reviewDetail.getReviewStatus() == REVIEW_STATUS_PENDING || reviewDetail.getReviewStatus() == REVIEW_STATUS_DENY) {
                return false;
            }
            // 居民身份证且信用未查询到相关信息不能复核
            if (reviewDetail.getReviewStatus() == REVIEW_STATUS_NOT_FOUND && authType == AUTH_TYPE_ID_CARD) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查驾照审核是否符合复核条件
     */
    private static boolean checkDriverLicenseReviewForReconsideration(OwnerQualificationReviewDetail reviewDetail) {
        // 驾照不通过不能复核
        return reviewDetail.getReviewType() != REVIEW_TYPE_DRIVER || reviewDetail.getReviewStatus() == REVIEW_STATUS_APPROVE;
    }

    /**
     * 检查居住证审核是否符合复核条件
     */
    private static boolean checkResidencePermitReviewForReconsideration(OwnerQualificationReviewDetail reviewDetail) {
        // 居住证不通过不能复核
        return reviewDetail.getReviewType() != REVIEW_TYPE_RESIDENCE || reviewDetail.getReviewStatus() == REVIEW_STATUS_APPROVE;
    }

    /**
     * 检查交委审核是否符合复核条件
     */
    private static boolean checkTrafficCommitteeReviewForReconsideration(OwnerQualificationReviewDetail reviewDetail) {
        // 交委不通过不能复核
        return reviewDetail.getReviewType() != REVIEW_TYPE_TRAFFIC_LICENSE || reviewDetail.getReviewStatus() == REVIEW_STATUS_APPROVE;
    }

    /**
     * 检查社保审核是否符合复核条件
     */
    private static boolean checkSocialInsuranceReviewForReconsideration(OwnerQualificationReviewDetail reviewDetail) {
        // 社保未完成不能复核
        return reviewDetail.getReviewType() != REVIEW_TYPE_SOCIAL || reviewDetail.getReviewStatus() != REVIEW_STATUS_PENDING;
    }

    /**
     * 自动分发并核查资格信息（无驾照信息）
     * 适用于企业用户等不需要驾照信息的场景
     *
     * @param assignOwnerQualification 指派的用户资格信息
     */
    private void autoAssignQualificationReview(OwnerQualification assignOwnerQualification) {
        autoAssignQualificationReview(assignOwnerQualification, null);
    }

    /**
     * 自动分发并核查资格信息
     * 根据资格类型和用户类型，分发到不同的审核服务进行核查
     *
     * @param assignOwnerQualification 指派的用户资格信息
     * @param driverLicense            驾驶证信息，企业用户可为null
     */
    private void autoAssignQualificationReview(OwnerQualification assignOwnerQualification, DriverLicenseDTO driverLicense) {
        // 根据资格类型选择不同的审核流程
        if (Objects.equals(assignOwnerQualification.getQualificationType(), OwnerQualificationTypeEnum.EXCHANGE.getType())) {
            // 以旧换新资质审核流程
            processExchangeQualificationReview(assignOwnerQualification, driverLicense);
        } else {
            // 普通购车资质审核流程
            processNormalQualificationReview(assignOwnerQualification, driverLicense);
        }
    }

    /**
     * 处理以旧换新资质审核流程
     * 以旧换新申请需要额外的车辆额度性质审核
     *
     * @param qualification 购车资格信息
     * @param driverLicense 驾照信息
     */
    private void processExchangeQualificationReview(OwnerQualification qualification, DriverLicenseDTO driverLicense) {
        // 基础审核项目
        reviewDriverLicense(qualification, driverLicense);
        reviewCreditInfo(qualification);
        reviewLicenseLimit(qualification);

        // 户籍相关审核
        reviewResidencePermitIfNeeded(qualification);
        reviewSocialInsuranceIfNeeded(qualification);

        // 以旧换新特有的审核项目
        trafficCommitteeService.reviewExchangeLimit(qualification.getId());
    }

    /**
     * 处理普通购车资质审核流程
     * 包括特殊人才的特殊处理逻辑
     *
     * @param qualification 购车资格信息
     * @param driverLicense 驾照信息
     */
    private void processNormalQualificationReview(OwnerQualification qualification, DriverLicenseDTO driverLicense) {
        // 检查是否为特殊人才
        if (isSpecialTalent(qualification)) {
            processSpecialTalentReview(qualification, driverLicense);
            return;
        }

        // 普通用户审核流程
        processRegularUserReview(qualification, driverLicense);
    }

    /**
     * 检查是否为特殊人才
     * @param qualification 购车资格信息
     * @return true表示是特殊人才，false表示普通用户
     */
    private boolean isSpecialTalent(OwnerQualification qualification) {
        return ownerQualificationSpecialService.isSpecialOwner(qualification.getAuthId());
    }

    /**
     * 处理特殊人才审核流程
     * 特殊人才略过常规校验，仅查询名下是否有新能源车
     *
     * @param qualification 购车资格信息
     * @param driverLicense 驾照信息
     */
    private void processSpecialTalentReview(OwnerQualification qualification, DriverLicenseDTO driverLicense) {
        logger.warn("该用户：{}为特殊人才，略过常规校验。", qualification.getAuthId());

        // 特殊人才仅需要查询驾照信息（仅查询名下是否有新能源车）
        if (qualification.getProperty() != 2) { // 非企业用户
            driverService.review(qualification.getId(), driverLicense, true);
        }
    }

    /**
     * 处理普通用户审核流程
     * 包含完整的审核项目
     *
     * @param qualification 购车资格信息
     * @param driverLicense 驾照信息
     */
    private void processRegularUserReview(OwnerQualification qualification, DriverLicenseDTO driverLicense) {
        // 基础审核项目
        reviewDriverLicense(qualification, driverLicense);
        reviewCreditInfo(qualification);
        reviewLicenseLimitForNonBusiness(qualification);

        // 户籍相关审核
        reviewResidencePermitIfNeeded(qualification);
        reviewSocialInsuranceForRegularUser(qualification);

        // 企业用户立即触发自动审核
        triggerAutoReviewForBusiness(qualification);
    }

    /**
     * 审核驾照信息（非企业用户）
     */
    private void reviewDriverLicense(OwnerQualification qualification, DriverLicenseDTO driverLicense) {
        if (qualification.getProperty() != 2) { // 非企业用户需要驾照审核
            driverService.review(qualification.getId(), driverLicense);
        }
    }

    /**
     * 审核信用信息
     */
    private void reviewCreditInfo(OwnerQualification qualification) {
        creditService.review(qualification.getId(), qualification.getProperty(),
                           qualification.getName(), qualification.getAuthId());
    }

    /**
     * 审核大牌额度（所有用户）
     */
    private void reviewLicenseLimit(OwnerQualification qualification) {
        trafficCommitteeService.reviewLicenseLimit(qualification.getId());
    }

    /**
     * 审核大牌额度（非企业用户）
     */
    private void reviewLicenseLimitForNonBusiness(OwnerQualification qualification) {
        if (qualification.getProperty() != 2) { // 非企业用户需要大牌额度审核
            trafficCommitteeService.reviewLicenseLimit(qualification.getId());
        }
    }

    /**
     * 根据需要审核居住证
     * 外省市（不包括港澳台）且非军官需要居住证审核
     */
    private void reviewResidencePermitIfNeeded(OwnerQualification qualification) {
        boolean needResidencePermitReview = qualification.getProperty() != 2 // 非企业用户
                && qualification.getHouseholdRegistrationType() == 1 // 外省市户籍
                && qualification.getIsMilitaryOfficer() == 0; // 非军官

        if (needResidencePermitReview) {
            residencePermitService.review(qualification.getId(),
                                        qualification.getName(), qualification.getAuthId());
        }
    }

    /**
     * 根据需要审核社保信息（以旧换新场景）
     */
    private void reviewSocialInsuranceIfNeeded(OwnerQualification qualification) {
        if (qualification.getHouseholdRegistrationType() != 0) { // 非上海市户籍
            newSocialService.review(qualification.getId(), qualification.getName(),
                                  qualification.getAuthType(), qualification.getAuthId());
        }
    }

    /**
     * 审核社保信息（普通购车场景）
     * 企业用户或外省市用户需要社保审核
     */
    private void reviewSocialInsuranceForRegularUser(OwnerQualification qualification) {
        boolean needSocialReview = qualification.getProperty() == 2 // 企业用户
                || qualification.getHouseholdRegistrationType() != 0; // 外省市用户

        if (needSocialReview) {
            newSocialService.review(qualification.getId(), qualification.getName(),
                                  qualification.getAuthType(), qualification.getAuthId());
        }
    }

    /**
     * 为企业用户触发自动审核
     * 企业用户审核完成后立即返回结果
     */
    private void triggerAutoReviewForBusiness(OwnerQualification qualification) {
        if (qualification.getProperty() == 2) { // 企业用户
            autoReviewOwnerQualification(qualification);
        }
    }

    /**
     * 保存用户资格信息
     * 包括业务规则验证、主表保存、关联表保存和操作日志记录
     *
     * @param saveOwnerQualification 购车资格信息
     * @param createdUserId 创建用户ID
     * @param createdUserName 创建用户姓名
     * @return 保存后的购车资格ID
     * @throws ServiceException 当车型不符合政策要求时抛出
     */
    private Long saveOwnerQualificationInfo(OwnerQualification saveOwnerQualification, String createdUserId, String createdUserName) {
        // 验证车型政策
        validateVehicleTypePolicy(saveOwnerQualification);

        // 设置基础信息并保存主表
        Long qualificationId = saveMainQualificationInfo(saveOwnerQualification, createdUserId, createdUserName);

        // 保存关联表信息
        saveRelatedTableInfo(saveOwnerQualification, createdUserId, createdUserName);

        // 记录操作日志
        saveOperationLog(qualificationId, createdUserId, createdUserName);

        return qualificationId;
    }

    /**
     * 验证车型政策
     * 检查车型是否符合当前政策要求
     *
     * @param qualification 购车资格信息
     * @throws ServiceException 当车型不符合政策要求时抛出
     */
    private void validateVehicleTypePolicy(OwnerQualification qualification) {
        // 混动车自2023年后拒绝申请
        if (ObjectUtils.nullSafeEquals(qualification.getVehicleModelType(), VEHICLE_TYPE_HYBRID)) {
            Date validDate = DateUtil.parse("2023-01-01", DateUtil.DATE_TYPE3);
            if (new Date().after(validDate)) {
                throw new ServiceException("根据《上海市鼓励购买和使用新能源汽车实施办法》（沪府办规〔2021〕3号）相关要求，自2023年1月1日起，不再接受插电式混合动力车辆申请。");
            }
        }
    }

    /**
     * 保存主表购车资格信息
     * 设置创建时间、更新时间等基础字段并插入数据库
     *
     * @param qualification 购车资格信息
     * @param createdUserId 创建用户ID
     * @param createdUserName 创建用户姓名
     * @return 保存后的购车资格ID
     */
    private Long saveMainQualificationInfo(OwnerQualification qualification, String createdUserId, String createdUserName) {
        Date now = new Date();

        // 设置基础字段
        qualification.setApplyTime(now);
        qualification.setCreatedTime(now);
        qualification.setCreatedUserId(createdUserId);
        qualification.setCreatedUserName(createdUserName);
        qualification.setUpdatedTime(now);
        qualification.setUpdatedUserId(createdUserId);
        qualification.setUpdatedUserName(createdUserName);

        // 插入主表
        ownerQualificationMapper.insertSelective(qualification);

        return qualification.getId();
    }

    /**
     * 保存关联表信息
     * 根据用户类型和资格类型保存相应的关联表数据
     *
     * @param qualification 购车资格信息
     * @param createdUserId 创建用户ID
     * @param createdUserName 创建用户姓名
     */
    private void saveRelatedTableInfo(OwnerQualification qualification, String createdUserId, String createdUserName) {
        // 保存交通委资格信息（非企业用户）
        saveTrafficCommitteeQualificationIfNeeded(qualification, createdUserId, createdUserName);

        // 保存以旧换新额度信息（以旧换新类型）
        saveExchangeQualificationIfNeeded(qualification);
    }

    /**
     * 保存交通委资格信息（如果需要）
     * 非企业用户需要保存到交通委推送表
     *
     * @param qualification 购车资格信息
     * @param createdUserId 创建用户ID
     * @param createdUserName 创建用户姓名
     */
    private void saveTrafficCommitteeQualificationIfNeeded(OwnerQualification qualification, String createdUserId, String createdUserName) {
        if (qualification.getProperty() != 2) { // 非企业用户
            Date now = new Date();
            OwnerTrafficCommitteeQualification trafficQualification = new OwnerTrafficCommitteeQualification();
            trafficQualification.setOwnerQualificationId(qualification.getId());
            trafficQualification.setCreatedUserId(createdUserId);
            trafficQualification.setCreatedUserName(createdUserName);
            trafficQualification.setCreatedTime(now);
            trafficQualification.setUpdatedUserId(createdUserId);
            trafficQualification.setUpdatedUserName(createdUserName);
            trafficQualification.setUpdatedTime(now);
            ownerTrafficCommitteeQualificationMapper.insertSelective(trafficQualification);
        }
    }

    /**
     * 保存以旧换新额度信息（如果需要）
     * 以旧换新类型的申请需要保存到交通委以旧换新表
     *
     * @param qualification 购车资格信息
     */
    private void saveExchangeQualificationIfNeeded(OwnerQualification qualification) {
        if (Objects.equals(qualification.getQualificationType(), OwnerQualificationTypeEnum.EXCHANGE.getType())) {
            Date now = new Date();
            OwnerTrafficCommitteeExchange exchangeQualification = new OwnerTrafficCommitteeExchange();
            exchangeQualification.setOwnerQualificationId(qualification.getId());
            exchangeQualification.setCreatedTime(now);
            exchangeQualification.setUpdatedTime(now);
            ownerTrafficCommitteeExchangeMapper.insertSelective(exchangeQualification);
        }
    }

    /**
     * 保存操作日志
     * 记录购车资格保存操作的日志信息
     *
     * @param qualificationId 购车资格ID
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     */
    private void saveOperationLog(Long qualificationId, String operatorId, String operatorName) {
        SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                .userOperateTypeEnum(UserOperateTypeEnum.SAVE_OWNER_QUALIFICATION)
                .ownerQualificationId(qualificationId)
                .operateUserId(operatorId)
                .operateUserName(operatorName)
                .build();
        userOperateLogService.saveOperateLog(saveOperateLogDTO);
    }

    /**
     * 获取未结束的意向用户信息
     *
     * @param authType 证件类型
     * @param authId   证件号码
     * @return OwnerQualification
     */
    @Override
    public OwnerQualification getUnfinishedQualification(Integer authType, String authId) {
        SelectStatementProvider selectStatement = select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where()
                .and(ownerQualification.authType, isEqualTo(authType))
                .and(ownerQualification.authId, isEqualTo(authId))
                .and(ownerQualification.status, isEqualTo(1))
                //未核查完毕的（未通过,未拒绝的）
                .and(ownerQualification.applyStatus, isNotIn(OwnerQualificationStatusEnum.DENY.getStatus(), OwnerQualificationStatusEnum.APPROVE.getStatus())
                        //核查拒绝但是未完成复核的
                        , or(ownerQualification.applyStatus, isEqualTo(OwnerQualificationStatusEnum.DENY.getStatus()), and(ownerQualification.expireTime, isGreaterThanOrEqualTo(new Date())))
                        //核查通过但是未过期的
                        , or(ownerQualification.applyStatus, isEqualTo(OwnerQualificationStatusEnum.APPROVE.getStatus()), and(ownerQualification.expireTime, isGreaterThanOrEqualTo(new Date())))
                )
                .and(ownerQualification.qualificationType, isEqualTo(OwnerQualificationTypeEnum.NORMAL.getType()))
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectOne(selectStatement);
        return optionalOwnerQualification.orElse(null);
    }

    @Override
    public List<OwnerQualification> queryOwnerQualification(Integer authType, String authId, Integer qualificationType) {
        SelectStatementProvider selectStatement = select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where()
                .and(ownerQualification.authType, isEqualTo(authType))
                .and(ownerQualification.authId, isEqualTo(authId))
                .and(ownerQualification.qualificationType, isEqualToWhenPresent(qualificationType))
                .and(ownerQualification.status,isEqualTo(1))
                .orderBy(ownerQualification.id.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return ownerQualificationMapper.selectMany(selectStatement);
    }

    @Override
    public QueryOwnerSocialDetailResponse queryOwnerSocialDetail(QueryOwnerSocialDetailDTO queryOwnerSocialDetailDTO) {
        QueryOwnerSocialDetailResponse response = new QueryOwnerSocialDetailResponse();
        String dateStart = StringUtils.EMPTY;
        if(queryOwnerSocialDetailDTO.getStartTime() != null){
            dateStart = DateUtil.format(queryOwnerSocialDetailDTO.getStartTime(), DateUtil.DATE_TYPE6);
        }
        String dateEnd = StringUtils.EMPTY;
        if(queryOwnerSocialDetailDTO.getEndTime() != null){
            dateEnd = DateUtil.format(queryOwnerSocialDetailDTO.getEndTime(), DateUtil.DATE_TYPE6);
        }

        NewSocialServiceImpl.SocialResponse taxationResponse = newSocialService.reviewSocialFromTaxation2025(
                queryOwnerSocialDetailDTO.getName(), queryOwnerSocialDetailDTO.getAuthType(), queryOwnerSocialDetailDTO.getAuthId(),
                dateStart, dateEnd);
        response.setTaxationResponse(taxationResponse);


        NewSocialServiceImpl.SocialResponse socialResponse = newSocialService.reviewSocialFromSocialAPI2025(
                queryOwnerSocialDetailDTO.getName(), queryOwnerSocialDetailDTO.getAuthType(), queryOwnerSocialDetailDTO.getAuthId(),
                dateStart, dateEnd);
        response.setSocialResponse(socialResponse);
        return response;
    }


    /**
     * 判断是否有未结束的意向用户记录
     *
     * @param authTypeValue 证件类型
     * @param authIdValue   证件号码
     * @return true:存在未结束的意向用户记录  false:不存在
     */
    private boolean existUnfinishedQualification(Integer authTypeValue, String authIdValue) {
        return getUnfinishedQualification(authTypeValue, authIdValue) != null;
    }

    /**
     * 复核意向用户
     *
     * @param ownerQualificationId the owner qualification id
     */
    private void reconsiderQualification(Long ownerQualificationId, String updatedUserId, String updatedUserName, String reconsiderOrgName) {
        //复核意向用户
        UpdateStatementProvider updateStatement = SqlBuilder.update(ownerQualification)
                .set(ownerQualification.applyStatus).equalTo(OwnerQualificationStatusEnum.RECONSIDERATION.getStatus())
                .set(ownerQualification.updatedTime).equalTo(new Date())
                .set(ownerQualification.updatedUserId).equalTo(updatedUserId)
                .set(ownerQualification.updatedUserName).equalTo(updatedUserName)
                .set(ownerQualification.reconsiderationImg).equalTo(reconsiderOrgName)
                .where(ownerQualification.id, isEqualTo(ownerQualificationId))
                .and(ownerQualification.applyStatus, isIn(OwnerQualificationStatusEnum.REVIEWING.getStatus(), OwnerQualificationStatusEnum.DENY.getStatus()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        ownerQualificationMapper.update(updateStatement);

        //保存操作日志
        SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                .userOperateTypeEnum(UserOperateTypeEnum.RECONSIDER_OWNER_QUALIFICATION)
                .ownerQualificationId(ownerQualificationId)
                .operateUserId(updatedUserId)
                .operateUserName(updatedUserName)
                .build();
        userOperateLogService.saveOperateLog(saveOperateLogDTO);
    }

    /**
     * 根据 ID 查询意向用户列表
     *
     * @param ownerQualificationIds 意向用户ID
     * @return List<OwnerQualification>
     */
    public List<OwnerQualification> queryOwnerQualifications(List<Long> ownerQualificationIds) {
        if (ownerQualificationIds.isEmpty()) {
            return new ArrayList<>();
        }
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where(ownerQualification.id, isIn(ownerQualificationIds))
                .and(ownerQualification.status,isEqualTo(1))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return ownerQualificationMapper.selectMany(selectStatement);
    }


    /**
     * 公安导入Excel自动核查
     * 根据公安部门提供的驾照信息进行自动审核，检查驾照有效性、违章记录等
     *
     * @param ownerQualification 购车资格信息
     * @param userFeedbackDTO 公安反馈的驾照信息
     * @return 审核结果：1-通过，2-拒绝，3-未查询到信息
     */
    private Integer trafficAutoApprove(OwnerQualification ownerQualification, OwnerTrafficQualification userFeedbackDTO) {
        // 检查是否有驾照信息
        if (!hasDriverLicense(userFeedbackDTO)) {
            return handleNoDriverLicense(userFeedbackDTO);
        }

        // 检查驾照各项状态
        TrafficReviewResult reviewResult = performTrafficReview(userFeedbackDTO, ownerQualification);

        // 设置审核结果
        if (!reviewResult.isSuccess()) {
            userFeedbackDTO.setReason(reviewResult.getReason());
            return 2; // 核查拒绝
        }

        return 1; // 核查成功
    }

    /**
     * 检查是否有驾照信息
     */
    private boolean hasDriverLicense(OwnerTrafficQualification userFeedbackDTO) {
        return Integer.valueOf(1).equals(userFeedbackDTO.getHasDriverLicense());
    }

    /**
     * 处理无驾照情况
     */
    private Integer handleNoDriverLicense(OwnerTrafficQualification userFeedbackDTO) {
        String reason = "未查询到驾照信息（驾驶证发证单位及核发地需保持一致。如您刚更新驾照信息，请在3个工作日后再查询，如您刚考取驾照，请在7个工作日后再查询。）";
        userFeedbackDTO.setReason(reason);
        return 3; // 查无此人
    }

    /**
     * 执行交通审核
     */
    private TrafficReviewResult performTrafficReview(OwnerTrafficQualification userFeedbackDTO, OwnerQualification ownerQualification) {
        StringBuilder reason = new StringBuilder();
        boolean isSuccess = true;

        // 检查驾照有效性
        if (!checkDriverLicenseValidity(userFeedbackDTO, reason)) {
            isSuccess = false;
        }

        // 检查违章记录
        if (!checkViolationRecord(userFeedbackDTO, reason)) {
            isSuccess = false;
        }

        // 检查处罚记录
        if (!checkPunishmentRecord(userFeedbackDTO, reason)) {
            isSuccess = false;
        }

        // 检查名下新能源车（仅普通购车资格）
        if (!checkNewEnergyVehicle(userFeedbackDTO, ownerQualification, reason)) {
            isSuccess = false;
        }

        String finalReason = isSuccess ? "" : reason.substring(0, reason.length() - 1);
        return new TrafficReviewResult(isSuccess, finalReason);
    }

    /**
     * 检查驾照有效性
     */
    private boolean checkDriverLicenseValidity(OwnerTrafficQualification userFeedbackDTO, StringBuilder reason) {
        boolean isValid = true;
        boolean hasLicenseIssue = false;

        // 检查驾照是否过期
        if (!Integer.valueOf(1).equals(userFeedbackDTO.getDriverLicenseNoExpire())) {
            reason.append("驾驶证已过期");
            isValid = false;
            hasLicenseIssue = true;
        }

        // 检查驾照是否有效
        if (!Integer.valueOf(1).equals(userFeedbackDTO.getDriverLicenseIsValidity())) {
            if (hasLicenseIssue) {
                reason.append("，");
            }
            reason.append("驾驶证已失效");
            isValid = false;
            hasLicenseIssue = true;
        }

        // 添加驾照问题的通用提示
        if (hasLicenseIssue) {
            reason.append("（驾驶证发证单位及核发地需保持一致。如您刚更新驾照信息，请在3个工作日后再查询）;");
        }

        return isValid;
    }

    /**
     * 检查违章记录
     */
    private boolean checkViolationRecord(OwnerTrafficQualification userFeedbackDTO, StringBuilder reason) {
        if (!Integer.valueOf(1).equals(userFeedbackDTO.getDriverLicenseHasViolation())) {
            reason.append("近一年违章数量大于等于5次;");
            return false;
        }
        return true;
    }

    /**
     * 检查处罚记录
     */
    private boolean checkPunishmentRecord(OwnerTrafficQualification userFeedbackDTO, StringBuilder reason) {
        if (!Integer.valueOf(1).equals(userFeedbackDTO.getHasPunish())) {
            reason.append("近一年产生过处罚;");
            return false;
        }
        return true;
    }

    /**
     * 检查名下新能源车（仅普通购车资格需要检查）
     */
    private boolean checkNewEnergyVehicle(OwnerTrafficQualification userFeedbackDTO, OwnerQualification ownerQualification, StringBuilder reason) {
        // 以旧换新不用查询名下有无新能源车
        if (Objects.equals(OwnerQualificationTypeEnum.NORMAL.getType(), ownerQualification.getQualificationType())) {
            if (!Integer.valueOf(1).equals(userFeedbackDTO.getHasNewEnergyVehicle())) {
                reason.append("名下有新能源汽车（如您刚将新能源汽车转出，请在3个工作日后再查询）;");
                return false;
            }
        }
        return true;
    }

    /**
     * 交通审核结果内部类
     */
    private static class TrafficReviewResult {
        private final boolean success;
        private final String reason;

        public TrafficReviewResult(boolean success, String reason) {
            this.success = success;
            this.reason = reason;
        }

        public boolean isSuccess() { return success; }
        public String getReason() { return reason; }
    }

    // ==================== 数据转换工具方法 ====================

    /**
     * 转换新能源车拥有状态
     * @param hasNewEnergyVehicle true表示有新能源车，false表示无新能源车
     * @return 0表示有新能源车（不符合条件），1表示无新能源车（符合条件）
     */
    private Integer tranHasNewEnergyVehicle(Boolean hasNewEnergyVehicle) {
        return hasNewEnergyVehicle ? 0 : 1;
    }


    @Override
    public SubmitExchangeQualificationResponse submitExchangeQualification(SubmitExchangeQualificationDTO submitExchangeQualificationDTO) {
        //校验车架号格式
        if(!VinFormatVerificationUtil.check(submitExchangeQualificationDTO.getExchangeVin())){
            throw new ServiceException("车辆识别代码格式错误");
        }
        //校验身份证号格式
        if(Objects.equals(submitExchangeQualificationDTO.getAuthType(), AuthTypeEnum.IDENTIFICATION_CARD.getType())
                && !IdCardUtil.isIDNumber(submitExchangeQualificationDTO.getAuthId())){
            throw new ServiceException("证件号格式错误");
        }
        //车牌号校验
        if(!submitExchangeQualificationDTO.getExchangeVehicleNo().startsWith("沪")){
            throw new ServiceException("该牌照暂不支持以旧换新政策");
        }
        if(!LicensePlateValidator.isValidLicensePlate(submitExchangeQualificationDTO.getExchangeVehicleNo())){
            throw new ServiceException("牌照号格式错误");
        }

        //校验上海市用户的户籍信息
        if(submitExchangeQualificationDTO.getHouseholdRegistrationType() == 0) {
            boolean isLocalHousehold = householdRegistrationService.isLocalHousehold(submitExchangeQualificationDTO.getName(), submitExchangeQualificationDTO.getAuthId());
            if (!isLocalHousehold) {
                log.error("用户：{}，户籍信息填写异常", submitExchangeQualificationDTO.getAuthId());
                throw new ServiceException("请确认户籍信息是否填写正确");
            }
        }


        //针对普通的私人用户，需要校验户籍信息
        if(submitExchangeQualificationDTO.getIsMilitaryOfficer() == 0
                && Objects.equals(AuthTypeEnum.IDENTIFICATION_CARD.getType(), submitExchangeQualificationDTO.getAuthType())){

            //校验外省市居住证信息
            if(submitExchangeQualificationDTO.getHouseholdRegistrationType() == 1){
                //校验是否拥有居住证
                boolean hasResidencePermit = residencePermitService.hasResidencePermit(submitExchangeQualificationDTO.getName(), submitExchangeQualificationDTO.getAuthId());
                if(!hasResidencePermit){
                    throw new ServiceException("外省市（非港澳台及其他外籍人士）必须持有有效上海市居住证才可办理");
                }
            }
        }

        //重新提交以旧换新资质 - 先逻辑删除原以旧换新数据，后创建一条新的以旧换新申请
        if(submitExchangeQualificationDTO.getId() != null){
            OwnerQualification exchangeQualification = checkExchangeQualification(submitExchangeQualificationDTO.getId(), submitExchangeQualificationDTO.getAuthType(), submitExchangeQualificationDTO.getAuthId());
            //校验当前状态能否重新提交
            if(Objects.equals(OwnerQualificationStatusEnum.APPROVE.getStatus(), exchangeQualification.getApplyStatus())) {
                if (exchangeQualification.getExpireTime() != null && exchangeQualification.getExpireTime().compareTo(new Date()) > 0) {
                    throw new ServiceException("当前状态无法重新提交");
                }
            }
            else if(Objects.equals(OwnerQualificationStatusEnum.DENY.getStatus(), exchangeQualification.getApplyStatus())){
                //根据有效期判断当前申请是否处于复核中状态
                if (exchangeQualification.getExpireTime() != null && exchangeQualification.getExpireTime().compareTo(new Date()) > 0) {
                    throw new ServiceException("请先申请放弃复核");
                }
            }
            else{
                throw new ServiceException("当前状态无法重新提交以旧换新申请");
            }

            //逻辑删除以旧换新申请信息
            deleteExchangeQualification(submitExchangeQualificationDTO.getId());

            //保存操作日志
            SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                    .userOperateTypeEnum(UserOperateTypeEnum.RESUME_EXCHANGE_QUALIFICATION)
                    .ownerQualificationId(exchangeQualification.getId())
                    .operateUserId(submitExchangeQualificationDTO.getOperatorId())
                    .operateUserName(submitExchangeQualificationDTO.getOperatorName())
                    .build();
            userOperateLogService.saveOperateLog(saveOperateLogDTO);
        }

        //校验是否已经提交过该车辆的以旧换新申请
        boolean existExchangeQualification = existExchangeQualification(null, submitExchangeQualificationDTO.getAuthType()
                , submitExchangeQualificationDTO.getAuthId(), submitExchangeQualificationDTO.getExchangeVin());
        if(existExchangeQualification){
            throw new ServiceException("您已经提交过该车辆的以旧换新申请，请勿重复提交");
        }

        //保存以旧换新资格基本信息
        OwnerQualification saveOwnerQualification = new OwnerQualification();
        saveOwnerQualification.setName(submitExchangeQualificationDTO.getName());
        saveOwnerQualification.setAuthId(submitExchangeQualificationDTO.getAuthId());
        saveOwnerQualification.setAuthType(submitExchangeQualificationDTO.getAuthType());
        saveOwnerQualification.setHouseholdRegistrationType(submitExchangeQualificationDTO.getHouseholdRegistrationType());
        saveOwnerQualification.setProperty(1);
        //是否是现役军人
        saveOwnerQualification.setIsMilitaryOfficer(submitExchangeQualificationDTO.getIsMilitaryOfficer());
        //资质类型：0:普通购车资质 1:以旧换新资格
        saveOwnerQualification.setQualificationType(OwnerQualificationTypeEnum.EXCHANGE.getType());
        saveOwnerQualification.setExchangeVin(submitExchangeQualificationDTO.getExchangeVin());
        saveOwnerQualification.setExchangeVehicleNo(submitExchangeQualificationDTO.getExchangeVehicleNo());
        saveOwnerQualification.setExchangeVehicleType(submitExchangeQualificationDTO.getExchangeVehicleType());
        Long ownerQualificationId = this.saveOwnerQualificationInfo(saveOwnerQualification, submitExchangeQualificationDTO.getOperatorId(), submitExchangeQualificationDTO.getOperatorName());

        //如果是军官证
        if(submitExchangeQualificationDTO.getIsMilitaryOfficer() == 1){
            //清除历史上传的扫描件
            fileService.clearFile(FileTypeEnum.MILITARY_OFFICER_COPY, ownerQualificationId, submitExchangeQualificationDTO.getOperatorId(), submitExchangeQualificationDTO.getOperatorName());
            // 保存军官证件照扫描件信息
            fileService.saveFileInfo(FileTypeEnum.MILITARY_OFFICER_COPY, submitExchangeQualificationDTO.getFile(), ownerQualificationId
                    , submitExchangeQualificationDTO.getOperatorId(), submitExchangeQualificationDTO.getOperatorName());
        }

        //组装驾照信息
        DriverLicenseDTO driverLicense = DriverLicenseDTO.builder()
                .driverLicenseCode(submitExchangeQualificationDTO.getDriverLicenseCode())
                .driverLicenseIssuingOrganization(submitExchangeQualificationDTO.getDriverLicenseIssuingOrganization())
                .driverLicenseIssuingPlace(submitExchangeQualificationDTO.getDriverLicenseIssuingPlace())
                .driverFileNo(submitExchangeQualificationDTO.getDriverFileNo())
                .build();
        //分发资质信息到各部委查询校验【主要逻辑！！！】
        autoAssignQualificationReview(saveOwnerQualification, driverLicense);

        return new SubmitExchangeQualificationResponse(ownerQualificationId);
    }


    /**
     * 逻辑删除以旧换新资质申请
     * @param ownerQualificationId 删除的ID
     */
    private void deleteExchangeQualification(Long ownerQualificationId){
        if(ownerQualificationId == null){
            return;
        }
        OwnerQualification updateOwnerQualification = new OwnerQualification();
        updateOwnerQualification.setId(ownerQualificationId);
        updateOwnerQualification.setStatus(0);
        updateOwnerQualification.setUpdatedTime(new Date());
        updateOwnerQualification.setUpdatedUserId(Const.ADMIN_ID);
        updateOwnerQualification.setUpdatedUserName(Const.SMALL_PROGRAM);
        ownerQualificationMapper.updateByPrimaryKeySelective(updateOwnerQualification);
    }

    @Override
    public SearchExchangeQualificationListResponse searchExchangeQualificationList(SearchExchangeQualificationListDTO searchExchangeQualificationListDTO) {
        List<SearchExchangeQualificationData> rows = new ArrayList<>();

        String authId = searchExchangeQualificationListDTO.getAuthId();
        Integer authType = searchExchangeQualificationListDTO.getAuthType();
        if(StringUtils.isBlank(authId) || authType == null){
            return new SearchExchangeQualificationListResponse(0L, rows);
        }

        //根据证件号、证件类型查询以旧换新资格申请列表
        SelectStatementProvider selectStatement = select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where(ownerQualification.authId, isEqualTo(authId))
                .and(ownerQualification.authType,isEqualTo(authType))
                .and(ownerQualification.qualificationType, isEqualTo(OwnerQualificationTypeEnum.EXCHANGE.getType()))
                .and(ownerQualification.status, isEqualTo(1))
                .build()
                .render(RenderingStrategies.MYBATIS3);

        List<OwnerQualification> ownerQualificationList = ownerQualificationMapper.selectMany(selectStatement);
        for (OwnerQualification qualification : ownerQualificationList) {
            SearchExchangeQualificationData qualificationData = ConvertUtil.normalConvert(qualification, SearchExchangeQualificationData.class);
            rows.add(qualificationData);
        }
        return new SearchExchangeQualificationListResponse((long) rows.size(), rows);
    }

    @Override
    public GetExchangeQualificationDetailResponse getExchangeQualificationDetail(Long ownerQualificationId, Integer authType, String authId) {
        //根据ID校验获取以旧换新资质信息
        OwnerQualification exchangeQualification = checkExchangeQualification(ownerQualificationId, authType, authId);
        //获取以旧换新资质详情
        OwnerQualificationDetailDTO ownerQualificationDetail = getOwnerQualificationDetail(Optional.of(exchangeQualification));
        GetExchangeQualificationDetailResponse response = ConvertUtil.normalConvert(ownerQualificationDetail, GetExchangeQualificationDetailResponse.class);
        boolean canReconsider = false;
        //只有【审核拒绝】的资质才允许复核
        if(Objects.equals(response.getApplyStatus(), OwnerQualificationStatusEnum.DENY.getStatus())){
            //若存在有效期，且未过期，则允许复核
            //【有效期】字段会在拒绝的时候，根据政策确认是否赋值
            canReconsider = response.getExpireTime() != null && new Date().before(response.getExpireTime());
        }
        response.setCanReconsider(canReconsider);
        return response;
    }

    @Override
    public void reconsiderExchangeQualification(ReconsiderExchangeQualificationDTO reconsiderExchangeQualificationDTO) {
        //根据ID校验获取以旧换新资质信息


        //校验当前状态能否进行复核

        //TODO
    }

    @Override
    public void cancelReconsiderExchangeQualification(CancelReconsiderExchangeQualificationDTO cancelReconsiderExchangeQualificationDTO) {
        //根据ID校验获取以旧换新资质信息
        OwnerQualification exchangeQualification = checkExchangeQualification(cancelReconsiderExchangeQualificationDTO.getOwnerQualificationId()
                , cancelReconsiderExchangeQualificationDTO.getAuthType(), cancelReconsiderExchangeQualificationDTO.getAuthId());
        //校验当前状态能否取消复核
        if(Objects.equals(exchangeQualification.getApplyStatus(), OwnerQualificationStatusEnum.DENY.getStatus())
            && exchangeQualification.getExpireTime() != null){
            Date now =new Date();
            //更新用户购车资格有效期，提前结束
            UpdateStatementProvider updateStatement = SqlBuilder.update(ownerQualification)
                    .set(ownerQualification.expireTime).equalTo(now)
                    .set(ownerQualification.updatedUserId).equalTo(cancelReconsiderExchangeQualificationDTO.getOperatorId())
                    .set(ownerQualification.updatedUserName).equalTo(cancelReconsiderExchangeQualificationDTO.getOperatorName())
                    .set(ownerQualification.updatedTime).equalTo(now)
                    .where(ownerQualification.id, isEqualTo(cancelReconsiderExchangeQualificationDTO.getOwnerQualificationId()))
                    .and(ownerQualification.expireTime, isNotNull())
                    .and(ownerQualification.expireTime, isGreaterThan(now))
                    .and(ownerQualification.applyStatus, isEqualTo(OwnerQualificationStatusEnum.DENY.getStatus()))
                    .build()
                    .render(RenderingStrategies.MYBATIS3);
            ownerQualificationMapper.update(updateStatement);

            //保存操作日志
            SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                    .userOperateTypeEnum(UserOperateTypeEnum.CANCEL_RECONSIDER_OWNER_QUALIFICATION)
                    .ownerQualificationId(cancelReconsiderExchangeQualificationDTO.getOwnerQualificationId())
                    .operateUserId(cancelReconsiderExchangeQualificationDTO.getOperatorId())
                    .operateUserName(cancelReconsiderExchangeQualificationDTO.getOperatorName())
                    .build();
            userOperateLogService.saveOperateLog(saveOperateLogDTO);
        }
    }

    @Override
    public void reconsiderExchangeFile(ReconsiderFileDTO reconsiderFileDTO) {
        if(reconsiderFileDTO.getReconsiderationFileCopy().isEmpty()){
            throw new ServiceException("请上传以旧换新证明材料");
        }

        OwnerQualification exchangeQualification = checkExchangeQualification(reconsiderFileDTO.getId());
        if(!Objects.equals(exchangeQualification.getApplyStatus(), OwnerQualificationStatusEnum.APPROVE.getStatus())){
            throw new ServiceException("以旧换新预审未通过，暂无法提交证明材料");
        }

        //判断材料是否已经过期
        if(exchangeQualification.getExpireTime() != null && new Date().after(exchangeQualification.getExpireTime())){
            throw new ServiceException("当前以旧换新资质已过期，请重新申请");
        }

        if(!Objects.equals(exchangeQualification.getExchangeStatus(), ExchangeStatusEnum.UNCOMMITTED.getStatus())
                && !Objects.equals(exchangeQualification.getExchangeStatus(), ExchangeStatusEnum.DENY.getStatus())){
            throw new ServiceException("当前状态暂无法提交证明材料，请刷新页面后重试");
        }


        //清空之前上传的图片
        fileService.clearFile(FileTypeEnum.EXCHANGE_PROOF_COPY, exchangeQualification.getId(), reconsiderFileDTO.getOperatorId(), reconsiderFileDTO.getOperatorName());
        //保存上传的文件
        List<String> collect = reconsiderFileDTO.getReconsiderationFileCopy().stream().map(FileInfoDTO::getRelativeFilePath).collect(Collectors.toList());
        fileService.saveFileInfo(FileTypeEnum.EXCHANGE_PROOF_COPY, collect, exchangeQualification.getId(), reconsiderFileDTO.getOperatorId(), reconsiderFileDTO.getOperatorName());

        //更新材料状态为待审核
        Date now = new Date();
        UpdateStatementProvider updateStatement = SqlBuilder.update(ownerQualification)
                .set(ownerQualification.exchangeStatus).equalTo(ExchangeStatusEnum.REVIEWING.getStatus())
                .set(ownerQualification.updatedTime).equalTo(now)
                .set(ownerQualification.updatedUserId).equalTo(reconsiderFileDTO.getOperatorId())
                .set(ownerQualification.updatedUserName).equalTo(reconsiderFileDTO.getOperatorName())
                .set(ownerQualification.exchangeReconsiderationOrg).equalTo(reconsiderFileDTO.getReconsiderOrgName())
                .set(ownerQualification.exchangeApplyTime).equalTo(now)
                .where(ownerQualification.id, isEqualTo(exchangeQualification.getId()))
                .and(ownerQualification.exchangeStatus, isIn(ExchangeStatusEnum.UNCOMMITTED.getStatus(), ExchangeStatusEnum.DENY.getStatus()))
                .and(ownerQualification.applyStatus, isEqualTo(OwnerQualificationStatusEnum.APPROVE.getStatus()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        ownerQualificationMapper.update(updateStatement);

        //保存操作日志
        SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                .userOperateTypeEnum(UserOperateTypeEnum.RECONSIDER_EXCHANGE_PROOF)
                .ownerQualificationId(exchangeQualification.getId())
                .operateUserId(reconsiderFileDTO.getOperatorId())
                .operateUserName(reconsiderFileDTO.getOperatorName())
                .build();
        userOperateLogService.saveOperateLog(saveOperateLogDTO);
    }


    @Override
    public void approveExchangeQualification(ApproveOwnerQualificationDTO approveOwnerQualificationDTO) {
        OwnerQualification exchangeQualification = checkExchangeQualification(approveOwnerQualificationDTO.getId());
        if(!Objects.equals(exchangeQualification.getApplyStatus(), OwnerQualificationStatusEnum.APPROVE.getStatus())){
            throw new ServiceException("以旧换新预审未通过，暂无法提交证明材料");
        }

        if(!Objects.equals(exchangeQualification.getExchangeStatus(), ExchangeStatusEnum.REVIEWING.getStatus())){
            throw new ServiceException("当前状态暂无法审核明材料，请刷新页面后重试");
        }

        //更新意向用户状态
        Date now = new Date();
        OwnerQualification approveOwnerQualification = new OwnerQualification();
        approveOwnerQualification.setId(exchangeQualification.getId());
        approveOwnerQualification.setExchangeStatus(ExchangeStatusEnum.APPROVE.getStatus());
        approveOwnerQualification.setExchangeReviewTime(now);
        approveOwnerQualification.setExchangeReason(StringUtils.EMPTY);
        approveOwnerQualification.setUpdatedTime(now);
        approveOwnerQualification.setUpdatedUserId(approveOwnerQualificationDTO.getOperatorId());
        approveOwnerQualification.setUpdatedUserName(approveOwnerQualificationDTO.getOperatorName());
        ownerQualificationMapper.updateByPrimaryKeySelective(approveOwnerQualification);

        //保存操作日志
        SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                .userOperateTypeEnum(UserOperateTypeEnum.APPROVE_EXCHANGE_QUALIFICATION)
                .ownerQualificationId(exchangeQualification.getId())
                .operateUserId(approveOwnerQualificationDTO.getOperatorId())
                .operateUserName(approveOwnerQualificationDTO.getOperatorName())
                .build();
        userOperateLogService.saveOperateLog(saveOperateLogDTO);
    }

    @Override
    public void denyExchangeQualification(DenyOwnerQualificationDTO denyOwnerQualificationDTO) {
        if(StringUtils.isBlank(denyOwnerQualificationDTO.getReason())){
            throw new ServiceException("请输入拒绝原因");
        }

        OwnerQualification exchangeQualification = checkExchangeQualification(denyOwnerQualificationDTO.getId());
        if(!Objects.equals(exchangeQualification.getApplyStatus(), OwnerQualificationStatusEnum.APPROVE.getStatus())){
            throw new ServiceException("以旧换新预审未通过，暂无法提交证明材料");
        }

        if(!Objects.equals(exchangeQualification.getExchangeStatus(), ExchangeStatusEnum.REVIEWING.getStatus())){
            throw new ServiceException("当前状态暂无法审核明材料，请刷新页面后重试");
        }

        //更新意向用户状态
        Date now = new Date();
        OwnerQualification approveOwnerQualification = new OwnerQualification();
        approveOwnerQualification.setId(exchangeQualification.getId());
        approveOwnerQualification.setExchangeStatus(ExchangeStatusEnum.DENY.getStatus());
        approveOwnerQualification.setExchangeReviewTime(now);
        approveOwnerQualification.setExchangeReason(denyOwnerQualificationDTO.getReason());
        approveOwnerQualification.setUpdatedTime(now);
        approveOwnerQualification.setUpdatedUserId(denyOwnerQualificationDTO.getOperatorId());
        approveOwnerQualification.setUpdatedUserName(denyOwnerQualificationDTO.getOperatorName());
        ownerQualificationMapper.updateByPrimaryKeySelective(approveOwnerQualification);

        //保存操作日志
        SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                .userOperateTypeEnum(UserOperateTypeEnum.DENY_EXCHANGE_QUALIFICATION)
                .ownerQualificationId(exchangeQualification.getId())
                .operateUserId(denyOwnerQualificationDTO.getOperatorId())
                .operateUserName(denyOwnerQualificationDTO.getOperatorName())
                .build();
        userOperateLogService.saveOperateLog(saveOperateLogDTO);
    }

    @Override
    public OwnerQualification queryAvailableExchangeQualification(Integer authKind, String authId) {
        if(authKind == null | StringUtils.isBlank(authId)){
            return null;
        }
        //查询第一条满足条件的以旧换新资质（可能存在多条）
        SelectStatementProvider selectStatement = select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where(ownerQualification.qualificationType, isEqualTo(OwnerQualificationTypeEnum.EXCHANGE.getType()))
                .and(status, isEqualTo(1))
                .and(ownerQualification.authId, isEqualTo(authId))
                .and(ownerQualification.authType,isEqualTo(authKind))
                .and(ownerQualification.exchangeBindVin, isNull())
                .and(ownerQualification.exchangeStatus, isEqualTo(ExchangeStatusEnum.APPROVE.getStatus()))
                .and(ownerQualification.applyStatus, isEqualTo(OwnerQualificationStatusEnum.APPROVE.getStatus()))
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return ownerQualificationMapper.selectOne(selectStatement).orElse(null);
    }

    @Override
    public void bindExchangeQualificationVin(String vin, Long id) {
        OwnerQualification updateOwnerQualification = new OwnerQualification();
        updateOwnerQualification.setId(id);
        updateOwnerQualification.setExchangeBindVin(vin);
        updateOwnerQualification.setUpdatedTime(new Date());
        updateOwnerQualification.setUpdatedUserId("-1");
        updateOwnerQualification.setUpdatedUserName("system");
        ownerQualificationMapper.updateByPrimaryKeySelective(updateOwnerQualification);
    }

    @Override
    public void unbindExchangeQualificationVin(Long id) {
        if(id == null){
            return;
        }
        UpdateStatementProvider updateStatement = update(ownerQualification)
                .set(ownerQualification.exchangeBindVin).equalToNull()
                .where(ownerQualification.id, isEqualTo(id))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        ownerQualificationMapper.update(updateStatement);
    }

    @Override
    public void invalidExchangeQualification(InvalidOwnerQualificationDTO invalidOwnerQualificationDTO) {
        OwnerQualification exchangeQualification = ownerQualificationMapper.selectByPrimaryKey(invalidOwnerQualificationDTO.getId()).orElse(null);
        if (exchangeQualification == null) {
            throw new ServiceException("未查询到相应用户信息");
        }
        if (exchangeQualification.getExpireTime() == null) {
            throw new ServiceException("该数据无法失效");
        }
        Date now = new Date();
        if (exchangeQualification.getExpireTime().compareTo(now) <= 0) {
            throw new ServiceException("该资格信息已过期，无需失效");
        }

        if(!Objects.equals(OwnerQualificationTypeEnum.EXCHANGE.getType(), exchangeQualification.getQualificationType())){
            throw new ServiceException("资格类型异常");
        }

        if(StringUtils.isNotBlank(exchangeQualification.getExchangeBindVin())){
            throw new ServiceException("该以旧换新资格已被使用，无法失效");
        }


        //更新用户购车资格有效期，提前结束
        UpdateStatementProvider updateStatement = SqlBuilder.update(ownerQualification)
                .set(ownerQualification.expireTime).equalTo(now)
                .set(ownerQualification.updatedUserId).equalTo(invalidOwnerQualificationDTO.getOperatorId())
                .set(ownerQualification.updatedUserName).equalTo(invalidOwnerQualificationDTO.getOperatorName())
                .set(ownerQualification.updatedTime).equalTo(now)
                .where(ownerQualification.id, isEqualTo(invalidOwnerQualificationDTO.getId()))
                .and(ownerQualification.expireTime, isNotNull())
                .and(ownerQualification.expireTime, isGreaterThan(now))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        ownerQualificationMapper.update(updateStatement);

        //保存操作日志
        SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                .userOperateTypeEnum(UserOperateTypeEnum.INVALID_OWNER_QUALIFICATION)
                .reason(invalidOwnerQualificationDTO.getReason())
                .ownerQualificationId(invalidOwnerQualificationDTO.getId())
                .operateUserId(invalidOwnerQualificationDTO.getOperatorId())
                .operateUserName(invalidOwnerQualificationDTO.getOperatorName())
                .build();
        userOperateLogService.saveOperateLog(saveOperateLogDTO);
    }

    /**
     * 根据车架号判断是否已经录入以旧换新信息
     * @param excludeId 排除的ID（自身)
     * @param authType 证件类型
     * @param authId 证件号
     * @param exchangeVin 以旧换新的车架号
     * @return true:已经存在以旧换新的记录  false:不存在以旧换新的记录
     */
    private boolean existExchangeQualification(@Nullable  Long excludeId, Integer authType, String authId, String exchangeVin){
        SelectStatementProvider selectStatement = select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where(ownerQualification.authType, isEqualTo(authType))
                .and(ownerQualification.authId, isEqualTo(authId))
                .and(ownerQualification.exchangeVin, isEqualTo(exchangeVin))
                .and(status, isEqualTo(1))
                //剔除本身的记录
                .and(ownerQualification.id , isNotEqualToWhenPresent(excludeId))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        List<OwnerQualification> ownerQualificationList = ownerQualificationMapper.selectMany(selectStatement);
        return !ownerQualificationList.isEmpty();
    }

    /**
     * 验证以旧换新资格信息
     * @param ownerQualificationId 以旧换新资质ID
     * @return 满足条件的以旧换新资格-若不满足条件，则抛出异常
     */
    private OwnerQualification checkExchangeQualification(Long ownerQualificationId){
        if(ownerQualificationId == null){
            throw new ServiceException("未查询到相关以旧换新资格信息");
        }
        OwnerQualification exchangeQualification = ownerQualificationMapper.selectByPrimaryKey(ownerQualificationId).orElse(null);
        if(exchangeQualification == null || exchangeQualification.getStatus() == 0){
            throw new ServiceException("未查询到相关以旧换新资格信息，请刷新页面重试");
        }
        if(!Objects.equals(OwnerQualificationTypeEnum.EXCHANGE.getType(), exchangeQualification.getQualificationType())){
            throw new ServiceException("资格类型异常");
        }
        return exchangeQualification;
    }

    /**
     * 验证以旧换新资格信息
     * @param ownerQualificationId 以旧换新资质ID
     * @param authType 提交人证件类型
     * @param authId 提交人证件号
     * @return 满足条件的以旧换新资格-若不满足条件，则抛出异常
     */
    private OwnerQualification checkExchangeQualification(Long ownerQualificationId, Integer authType, String authId){
        OwnerQualification exchangeQualification = checkExchangeQualification(ownerQualificationId);
        if(!Objects.equals(authType, exchangeQualification.getAuthType()) || !Objects.equals(authId, exchangeQualification.getAuthId())){
            throw new ServiceException("您暂无权限查询该条信息，请联系管理员");
        }
        return exchangeQualification;
    }

}
