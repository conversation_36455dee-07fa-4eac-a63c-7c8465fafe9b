package com.extracme.nevmp.controller;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.auth.UserSession;
import com.extracme.nevmp.converter.ConvertUtil;
import com.extracme.nevmp.dto.authority.*;
import com.extracme.nevmp.dto.common.PageInfoBO;
import com.extracme.nevmp.service.authority.AuthorityService;
import com.extracme.nevmp.utils.CommonUtil;
import com.extracme.nevmp.vo.common.SingleId;
import com.extracme.nevmp.vo.authority.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

/**
 * 权限
 * <AUTHOR>
 * @date 2020/8/27
 */
@RestController
@RequestMapping("authority")
public class AuthorityController {

    @Autowired
    private AuthorityService authorityService;


    /**
     * 生成唯一建
     * @return
     */
    @PostMapping("generateResourceKey")
    public UuidResponse generateResourceKey() {
        UuidResponse uuidResponse = new UuidResponse();
        uuidResponse.setUuid(UUID.randomUUID().toString());
        return uuidResponse;
    }

    /**
     * 新增资源
     * @return
     */
    @PostMapping("/addResource")
    public BaseResponse addResource(@RequestBody @Validated ResourceNodeVO resourceInfoVO) {
        ResourceNode resourceNode = new ResourceNode();
        BeanUtils.copyProperties(resourceInfoVO, resourceNode);
        authorityService.addResource(resourceNode);
        return new BaseResponse();
    }

    /**
     * 更新资源
     * @param resourceNodeVO
     * @return
     */
    @PostMapping("updateResource")
    public BaseResponse updateResource(@RequestBody @Validated ResourceNodeVO resourceNodeVO) {
        ResourceNode resourceNode = new ResourceNode();
        BeanUtils.copyProperties(resourceNodeVO, resourceNode);
        return authorityService.updateResource(resourceNode);
    }

    /**
     * 删除资源
     * @param deleteResourceVO
     * @return
     */
    @PostMapping("deleteResource")
    public BaseResponse deleteResource(@RequestBody @Validated DeleteResourceVO deleteResourceVO) {
        DeleteResourceDTO deleteResourceDTO = new DeleteResourceDTO();
        deleteResourceDTO.setId(deleteResourceVO.getId());
        return authorityService.deleteResource(deleteResourceDTO);
    }

    /**
     * 获取资源树
     *
     * @return
     */
    @PostMapping("/getResourcesTree")
    public BaseResponse getResourcesTree() {
        return authorityService.getResourcesTree();
    }




    /**
     * 新增角色
     *
     * @return
     */
    @PostMapping("addRole")
    public BaseResponse addRole(@RequestBody @Validated AddRoleVO addRoleVO) {
        AddRoleDTO addRoleDTO = new AddRoleDTO();
        BeanUtils.copyProperties(addRoleVO, addRoleDTO);
        authorityService.addRole(addRoleDTO);
        return new BaseResponse();
    }

    /**
     * 获取角色列表
     * @return
     */
    @PostMapping("getAllRole")
    public BaseResponse getAllRole(@RequestBody SearchRoleVO searchRoleVO, @SessionAttribute("user") UserSession userSession) {
        SearchRoleDTO searchRoleDTO = ConvertUtil.convert(searchRoleVO, SearchRoleDTO.class);
        searchRoleDTO.setLoginUser(userSession.getUserName());
        return authorityService.getAllRole(searchRoleDTO);
    }


    /**
     * 更新角色对应的权限
     *
     * @param updateRoleResourceVO
     * @return
     */
    @PostMapping("updateRoleResource")
    public BaseResponse updateRoleResource(@RequestBody UpdateRoleResourceVO updateRoleResourceVO, @SessionAttribute("user") UserSession userSession) {
        UpdateRoleResourceDTO updateRoleResourceDTO = new UpdateRoleResourceDTO();
        BeanUtils.copyProperties(updateRoleResourceVO, updateRoleResourceDTO);
        updateRoleResourceDTO.setUserId(userSession.getUserId());
        updateRoleResourceDTO.setUserName(userSession.getUserName());
        authorityService.updateRoleResource(updateRoleResourceDTO);
        return new BaseResponse();
    }

    /**
     * 更新角色信息
     * @param updateRoleVO
     * @return
     */
    @PostMapping("updateRoleInfo")
    public BaseResponse updateRoleInfo(@RequestBody @Validated UpdateRoleVO updateRoleVO) {
        UpdateRoleDTO updateRoleDTO = new UpdateRoleDTO();
        BeanUtils.copyProperties(updateRoleVO, updateRoleDTO);
        authorityService.updateRoleInfo(updateRoleDTO);
        return new BaseResponse();
    }


    /**
     * 获取角色对应的资源树
     * @return
     */
//    @PostMapping("getResourceTreeByRole")
    public BaseResponse getResourceTreeByRole(@RequestBody @Validated SingleId<String> roleId) {
        return authorityService.getResourceTreeByRole(roleId.getId());
    }

    /**
     * 获取角色对应的资源ID
     *
     * @param roleId
     * @return
     */
    @PostMapping("/getResourcesByRole")
    public BaseResponse getResourcesByRole(@RequestBody @Validated SingleId<String> roleId) {
        return authorityService.getResourcesByRole(roleId.getId());
    }


    /**
     * 获取用户列表
     *
     * @return
     */
    @PostMapping("/searchUser")
    public BaseResponse searchUser(@RequestBody SearchUserVO searchUserVO) {
        SearchUserDTO searchUserDTO = new SearchUserDTO();
        searchUserDTO.setLoginName(StringUtils.isBlank(searchUserVO.getLoginName()) ? null : searchUserVO.getLoginName());
        searchUserDTO.setOrgId(searchUserVO.getOrgId());
        searchUserDTO.setLimit(searchUserVO.getPageSize());
        searchUserDTO.setOffset(CommonUtil.getOffset(searchUserVO.getPageNum(), searchUserVO.getPageSize()));
        return authorityService.searchUser(searchUserDTO);
    }

    /**
     * 获取用户详情
     * @return
     */
    @PostMapping("getUserRole")
    public BaseResponse getUserDetail(@RequestBody SingleId<String> userId) {
        UserRoleDTO userRoleDTO =  authorityService.getUserRoleDetail(userId.getId());
        return userRoleDTO;
    }

    /**
     * 更新用户信息
     *
     * @return
     */
    @PostMapping("updateUserRole")
    public BaseResponse updateUserRole(@RequestBody UpdateUserRoleVO updateUserRoleVO) {
        UpdateUserRoleDTO updateUserRoleDTO = new UpdateUserRoleDTO();
        BeanUtils.copyProperties(updateUserRoleVO, updateUserRoleDTO);
        authorityService.updateUserRole(updateUserRoleDTO);
        return new BaseResponse();
    }
}
